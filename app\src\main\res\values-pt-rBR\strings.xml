<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name" translatable="false">APatch</string>
    <string name="home">Início</string>
    <string name="patch_warnning">A instalação apresenta riscos. Por favor, certifique-se de fazer backup de seus dados.</string>
    <string name="patch_title">Patch</string>
    <string name="kpm_author">Autor</string>
    <string name="kpm_desc">Descrição</string>
    <string name="kpm_args">Argumentos</string>
    <string name="su_title">SuperUsuário</string>
    <string name="su_pkg_excluded_setting_summary">Ativar esta opção permitirá que o APatch restaure quaisquer arquivos modificados pelos módulos deste app.</string>
    <string name="su_refresh">Atualizar</string>
    <string name="apm_reboot_to_apply">Reinicie para entrar em vigor</string>
    <string name="kernel_patch">KernelPatch</string>
    <string name="android_patch">AndroidPatch</string>
    <string name="reboot">Reiniciar</string>
    <string name="settings">Configurações</string>
    <string name="reboot_recovery">Reiniciar em modo Recovery</string>
    <string name="reboot_bootloader">Reiniciar em modo Bootloader</string>
    <string name="reboot_download">Reiniciar em modo Download</string>
    <string name="reboot_edl">Reiniciar em modo EDL</string>
    <string name="about">Sobre</string>
    <string name="home_learn_apatch">Saiba mais sobre o APatch</string>
    <string name="home_click_to_learn_apatch">Conheça os recursos do APatch e como usá-lo</string>
    <string name="about_source_code"><![CDATA[<p>Veja o código-fonte no %1$s<p/>Participe do nosso canal do %2$s<p/>Participe do nosso grupo do %3$s]]></string>
    <string name="send_log">Reportar registros</string>
    <string name="safe_mode">Modo de segurança</string>
    <string name="super_key">SuperKey</string>
    <string name="patch_set_superkey">Definir SuperKey</string>
    <string name="home_patch_set_key_desc">Apenas credencial para KernelPatch</string>
    <string name="home_patch_next_step">Próximo passo</string>
    <string name="home_not_installed">Não instalado</string>
    <string name="home_click_to_install">Clique para instalar</string>
    <string name="home_working">Em execução 😋</string>
    <string name="home_install_unknown">Não instalado ou autenticado</string>
    <string name="home_installing">Instalando</string>
    <string name="home_need_update">Nova versão disponível</string>
    <string name="kpatch_version">Versão: %s</string>
    <string name="apatch_version">Versão: %s</string>
    <string name="home_su_path">Su executável</string>
    <string name="apatch_version_update">Versão: %s -&gt; %s</string>
    <string name="home_apatch_version">Versão do APatch</string>
    <string name="home_auth_key_title">Inserir SuperKey</string>
    <string name="home_auth_key_desc">Iniciar após a certificação</string>
    <string name="home_ap_cando_install">Instalar</string>
    <string name="home_ap_cando_update">Atualizar</string>
    <string name="home_ap_cando_uninstall">Desinstalar</string>
    <string name="home_kernel">Kernel</string>
    <string name="home_manager_version">Versão do gerenciador</string>
    <string name="home_fingerprint">Impressão digital</string>
    <string name="home_selinux_status">Status do SELinux</string>
    <string name="home_selinux_status_disabled">Desativado</string>
    <string name="home_selinux_status_enforcing">Impondo</string>
    <string name="home_selinux_status_permissive">Permissivo</string>
    <string name="home_selinux_status_unknown">Desconhecido</string>
    <string name="kpm">KPMódulo</string>
    <string name="kpm_kp_not_installed">KernelPatch não instalado</string>
    <string name="kpm_unload">Descarregar</string>
    <string name="kpm_load">Carregar</string>
    <string name="kpm_load_toast_succ">Carregado com sucesso</string>
    <string name="kpm_load_toast_failed">Erro ao carregar</string>
    <string name="kpm_unload_confirm">Descarregar o módulo %s?</string>
    <string name="kpm_apm_empty">Nenhum módulo carregado</string>
    <string name="kpm_version">Versão</string>
    <string name="kpm_license">Licença</string>
    <string name="su_selinux_via_hook">ignorar via hook</string>
    <string name="su_pkg_excluded_label">excluir</string>
    <string name="su_pkg_excluded_setting_title">Excluir modificações</string>
    <string name="su_show_system_apps">Mostrar apps do sistema</string>
    <string name="su_hide_system_apps">Ocultar apps do sistema</string>
    <string name="apm">APMódulo</string>
    <string name="apm_failed_to_enable">Não foi possível ativar o módulo %s</string>
    <string name="apm_failed_to_disable">Não foi possível desativar o módulo %s</string>
    <string name="apm_empty">Nenhum módulo instalado</string>
    <string name="apm_remove">Desinstalar</string>
    <string name="apm_install">Instalar</string>
    <string name="apm_uninstall_confirm">Desinstalar o módulo %s?</string>
    <string name="apm_uninstall_success">%s desinstalado</string>
    <string name="apm_uninstall_failed">Não foi possível desinstalar %s</string>
    <string name="apm_version">Versão</string>
    <string name="apm_author">Autor</string>
    <string name="apm_desc">Descrição</string>
    <string name="apm_overlay_fs_not_available">Os módulos estão indisponíveis porque OverlayFS está desabilitado pelo kernel!</string>
    <string name="apm_magisk_conflict">Os módulos estão indisponíveis devido a um conflito com Magisk!</string>
    <string name="apm_changelog">Registro de alterações</string>
    <string name="apm_update">Atualizar</string>
    <string name="apm_downloading">Baixando módulo %s</string>
    <string name="apm_start_downloading">Começando a baixar %s</string>
    <string name="apm_new_version_available">Nova versão %s está disponível, clique para atualizar.</string>
    <string name="clear_super_key">Limpar SuperKey</string>
    <string name="apm_not_installed">AndroidPatch não instalado</string>
    <string name="settings_app_language">Idioma</string>
    <string name="system_default">Padrão do sistema</string>
    <string name="patch">Patch</string>
    <string name="settings_global_namespace_mode">Modo de namespace global</string>
    <string name="settings_global_namespace_mode_summary">Todas as sessões root usam o namespace de montagem global</string>
    <string name="kpatch_version_update">Versão: %s -&gt; %s</string>
    <string name="kpatch_shadow_path_title">kpatch</string>
    <string name="home_kpatch_info_title">Informações</string>
    <string name="home_ap_cando_reboot">Reiniciar</string>
    <string name="patch_item_error">!!ERRO!!</string>
    <string name="patch_item_bootimg">bootimg</string>
    <string name="patch_item_bootimg_slot">slot:</string>
    <string name="patch_item_bootimg_dev">dispositivo:</string>
    <string name="patch_item_kernel">Kernel</string>
    <string name="patch_item_kpimg">kpimg</string>
    <string name="patch_item_kpimg_version">Versão:</string>
    <string name="patch_item_kpimg_comile_time">Hora:</string>
    <string name="patch_item_kpimg_config">Configuração:</string>
    <string name="patch_item_new_extra_kpm">Incorporar novo</string>
    <string name="patch_item_existed_extra_kpm">Existia</string>
    <string name="patch_item_extra_name">Nome:</string>
    <string name="patch_item_extra_version">Versão:</string>
    <string name="patch_item_extra_author">Autor:</string>
    <string name="patch_item_extra_kpm_license">Licença:</string>
    <string name="patch_item_extra_kpm_desciption">Descrição:</string>
    <string name="patch_select_bootimg_btn">Selecionar boot</string>
    <string name="patch_embed_kpm_btn">Incorporar KPM</string>
    <string name="patch_start_patch_btn">Iniciar</string>
    <string name="patch_config_title">Patches</string>
    <string name="patch_mode_bootimg_patch">Modo: Patch</string>
    <string name="patch_mode_patch_and_install">Modo: Patch e instalação</string>
    <string name="patch_mode_uninstall_patch">Modo: Desinstalar KPatch</string>
    <string name="patch_start_unpatch_btn">UnPatch</string>
    <string name="patch_item_extra_args">Argumentos:</string>
    <string name="patch_item_extra_event">Evento:</string>
    <string name="patch_item_skey">SuperKey</string>
    <string name="settings_clear_super_key_dialog">Você realmente deseja continuar?</string>
    <string name="patch_item_set_skey_label">A SuperKey deve ter de 8 a 63 caracteres e incluir números e letras, mas sem caracteres especiais.</string>
    <string name="hide_apatch_dialog_new_manager_name">Nome do novo gerenciador</string>
    <string name="hide_apatch_dialog_summary">Ele será usado como o novo nome do app mostrado na launcher</string>
    <string name="hide_apatch_manager">Ocultar app do APatch</string>
    <string name="hide_apatch_manager_summary">Instala o app oculto com ID aleatório e nome personalizado</string>
    <string name="hide_apatch_manager_failure">Falha ao ocultar. Por favor, reporte o bug!</string>
    <string name="kpm_embed">Incorporar</string>
    <string name="kpm_add_kpm">Adicionar KPM</string>
    <string name="kpm_install">Instalar</string>
    <string name="failure">Falha</string>
    <string name="setting_reset_su_new_path">Novo caminho completo</string>
    <string name="success">Sucesso</string>
    <string name="setting_reset_su_path">Redefinir caminho su</string>
    <string name="settings_check_update_summary">Verifique automaticamente se há atualizações ao abrir o app</string>
    <string name="settings_check_update">Verificar por atualização</string>
    <string name="mode_select_page_install_inactive_slot">Instalar no slot inativo (após o OTA)</string>
    <string name="mode_select_page_install_inactive_slot_warning">Seu dispositivo será **FORÇADO** a inicializar no slot inativo atual após uma reinicialização!
\nSó use esta opção após a conclusão do OTA.
\nDeseja continuar?</string>
    <string name="home_dialog_auth_fail_title">Falha na autenticação</string>
    <string name="mode_select_page_select_file">Selecionar imagem boot para Patch</string>
    <string name="mode_select_page_title">Instalar</string>
    <string name="mode_select_page_patch_and_install">Patch e instalação</string>
    <string name="home_dialog_auth_fail_content">Não foi possível autenticar a SuperKey, portanto o APatch não pode funcionar corretamente. \nAqui estão alguns possíveis motivos para a falha de autenticação:\n1. Você não fez patch do boot.img com KernelPatch.\n2. O boot.img corrigido não foi flashado.\n3. A SuperKey está incorreta ou contém caracteres especiais.\n4. Seu dispositivo não oferece suporte ao APatch e KernelPatch.\n\nPor favor, verifique e tente novamente. Se ainda houver problemas, você pode fazer perguntas na página de problemas do repositório oficial.</string>
    <string name="home_more_menu_feedback_or_suggestion">Feedback ou sugestão</string>
    <string name="home_more_menu_about">Sobre</string>
    <string name="apm_webui_open">Abrir</string>
    <string name="enable_web_debugging">Ativar depuração do WebView</string>
    <string name="settings_donot_store_superkey_summary">Autentique a SuperKey sempre que o gerenciador iniciar</string>
    <string name="enable_web_debugging_summary">Pode ser usado para depurar o WebUI. Por favor, ative somente quando necessário.</string>
    <string name="settings_donot_store_superkey">Não armazenar a SuperKey no local</string>
    <string name="home_new_apatch_found">Nova versão %s está disponível, clique para atualizar.</string>
    <string name="patch_mode_install_to_next_slot">Modo: Instalar no slot inativo (após o OTA)</string>
    <string name="home_device_info">Dispositivo</string>
    <string name="home_dialog_uninstall_ap_only">Desinstalar AndroidPatch</string>
    <string name="home_dialog_uninstall_all">Desinstalar tudo</string>
    <string name="home_install_unknown_summary">Clique para instalar</string>
    <string name="home_system_version">Versão do sistema</string>
    <string name="home_kpatch_version">Versão do KernelPatch</string>
    <string name="home_dialog_uninstall_title">Desinstalar</string>
    <string name="settings_night_theme_enabled">Ativar tema escuro</string>
    <string name="settings_use_system_color_theme">Tema de cores do sistema</string>
    <string name="settings_use_system_color_theme_summary">Use um tema de cores gerado pelo sistema com base no plano de fundo atual</string>
    <string name="settings_custom_color_theme">Tema de cores</string>
    <string name="green_theme">Verde</string>
    <string name="deep_orange_theme">Laranja profundo</string>
    <string name="lime_theme">Verde-limão</string>
    <string name="orange_theme">Laranja</string>
    <string name="teal_theme">Verde-azulado</string>
    <string name="kpm_control_failed">Operação falhou!</string>
    <string name="settings_night_mode_follow_sys_summary">Mude automaticamente para o tema escuro com base nas configurações do sistema</string>
    <string name="blue_theme">Azul</string>
    <string name="cyan_theme">Ciano</string>
    <string name="light_blue_theme">Azul claro</string>
    <string name="light_green_theme">Verde claro</string>
    <string name="pink_theme">Rosa</string>
    <string name="purple_theme">Roxo</string>
    <string name="red_theme">Vermelho</string>
    <string name="sakura_theme">Sakura</string>
    <string name="about_app_version">Versão %1$s</string>
    <string name="about_telegram_group">Grupo do Telegram</string>
    <string name="about_powered_by">Fornecido por %1$s</string>
    <string name="yellow_theme">Amarelo</string>
    <string name="about_telegram_channel">Canal do Telegram</string>
    <string name="kpm_control">Controlar</string>
    <string name="kpm_control_dialog_content">Parâmetros de controle de entrada:</string>
    <string name="kpm_control_paramters">Parâmetros</string>
    <string name="kpm_control_outMsg">Mensagem</string>
    <string name="kpm_control_ok">Operação bem-sucedida!</string>
    <string name="settings_night_mode_follow_sys">Seguir tema escuro do sistema</string>
    <string name="amber_theme">Âmbar</string>
    <string name="blue_grey_theme">Cinza-azulado</string>
    <string name="indigo_theme">Azul-anil</string>
    <string name="brown_theme">Marrom</string>
    <string name="deep_purple_theme">Roxo profundo</string>
    <string name="about_app_desc">Solução root do kernel com uma única imagem despojada, oferecendo a capacidade de montar módulos do sistema e interceptar funções do kernel.</string>
    <string name="kpm_control_dialog_title">Controlar KPMódulo</string>
    <string name="save_log">Salvar registros</string>
    <string name="apm_action">Ação</string>
    <string name="crash_handle_copied">Registro de falhas copiado.</string>
    <string name="crash_handle_title">O app travou…</string>
    <string name="crash_handle_copy">Copiar registro de falhas</string>
    <string name="log_saved">Registros salvos</string>
</resources>
