<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name" translatable="false">APatch</string>

    <string name="home">Home</string>

    <string name="success">Success</string>
    <string name="failure">Failure</string>

    <string name="patch_warnning">Installation comes with risks. Please ensure your data is backed up.</string>
    <string name="patch">Patch</string>

    <string name="kernel_patch">KernelPatch</string>
    <string name="android_patch">AndroidPatch</string>

    <string name="reboot">Reboot</string>
    <string name="settings">Settings</string>
    <string name="reboot_recovery">Reboot to Recovery</string>
    <string name="reboot_bootloader">Reboot to Bootloader</string>
    <string name="reboot_download">Reboot to Download</string>
    <string name="reboot_edl">Reboot to EDL</string>
    <string name="about">About</string>
    <string name="settings_app_language">Language</string>
    <string name="system_default">System default</string>
    <string name="settings_global_namespace_mode">Global namespace mode</string>
    <string name="settings_global_namespace_mode_summary">All root sessions use the global mount namespace</string>
    <string name="settings_clear_super_key_dialog">Do you really want to proceed?</string>

    <string name="settings_check_update">Check for updates</string>
    <string name="settings_check_update_summary">Automatically check for updates when opening the app</string>

    <string name="home_learn_apatch">Learn APatch</string>
    <string name="home_click_to_learn_apatch">Learn about the features of APatch and how to use</string>

    <string name="about_source_code"><![CDATA[<p>View source code at %1$s<p/>Join our %2$s channel<p/>Join our %3$s group]]></string>
    <string name="send_log">Send logs</string>
    <string name="save_log">Save logs</string>
    <string name="log_saved">Logs saved</string>
    <string name="safe_mode">Safe mode</string>

    <string name="super_key">SuperKey</string>
    <string name="clear_super_key">Clear SuperKey</string>
    <string name="patch_set_superkey">Set SuperKey</string>
    <string name="home_patch_set_key_desc">Only credential for KernelPatch</string>
    <string name="home_patch_next_step">Next step</string>

    <string name="home_not_installed">Not installed</string>
    <string name="home_install_unknown">Not installed or authenticated</string>
    <string name="home_install_unknown_summary">Click to install</string>
    <string name="home_click_to_install">Click to install</string>
    <string name="home_working">Working 😋</string>

    <string name="home_installing">Installing</string>
    <string name="home_need_update">New version available</string>
    <string name="kpatch_version">Version: %s</string>
    <string name="apatch_version">Version: %s</string>
    <string name="apatch_version_update">Version: %s -&gt; %s</string>
    <string name="kpatch_version_update">Version: %s -&gt; %s</string>
    <string name="kpatch_shadow_path_title">kpatch</string>
    <string name="home_auth_key_title">Enter SuperKey</string>
    <string name="home_auth_key_desc">Start after certification</string>
    <string name="home_kpatch_info_title">Info</string>

    <string name="home_ap_cando_install">Install</string>
    <string name="home_ap_cando_update">Update</string>
    <string name="home_ap_cando_uninstall">Uninstall</string>
    <string name="home_ap_cando_reboot">Reboot</string>

    <string name="patch_title">Patch</string>

    <string name="patch_config_title">Patches</string>
    <string name="patch_mode_bootimg_patch">Mode: Patch</string>
    <string name="patch_mode_patch_and_install">Mode: Patch and install</string>
    <string name="patch_mode_install_to_next_slot">Mode: Install to inactive slot (After OTA)</string>
    <string name="patch_mode_uninstall_patch">Mode: Uninstall KPatch</string>
    <string name="patch_select_bootimg_btn">Select boot</string>
    <string name="patch_embed_kpm_btn">Embed KPM</string>
    <string name="patch_start_patch_btn">Start</string>
    <string name="patch_start_unpatch_btn">UnPatch</string>
    <string name="patch_item_error">!!ERROR!!</string>
    <string name="patch_item_bootimg">bootimg</string>
    <string name="patch_item_bootimg_slot">slot:</string>
    <string name="patch_item_bootimg_dev">device:</string>
    <string name="patch_item_kernel">Kernel</string>
    <string name="patch_item_kpimg">kpimg</string>
    <string name="patch_item_kpimg_version">Version:</string>
    <string name="patch_item_kpimg_comile_time">Time:</string>
    <string name="patch_item_kpimg_config">Config:</string>
    <string name="patch_item_new_extra_kpm">Embed new</string>
    <string name="patch_item_existed_extra_kpm">Existed</string>
    <string name="patch_item_extra_name">Name:</string>
    <string name="patch_item_extra_version">Version:</string>
    <string name="patch_item_extra_author">Author:</string>
    <string name="patch_item_extra_kpm_license">License:</string>
    <string name="patch_item_extra_kpm_desciption">Description:</string>
    <string name="patch_item_extra_args">Args:</string>
    <string name="patch_item_extra_event">Event:</string>
    <string name="patch_item_skey">SuperKey</string>
    <string name="patch_item_set_skey_label">The SuperKey should be 8-63 characters long and include numbers and letters, but no special characters.</string>

    <string name="home_kernel">Kernel version</string>
    <string name="home_manager_version">Manager version</string>
    <string name="home_fingerprint">Fingerprint</string>

    <string name="home_selinux_status">SELinux status</string>
    <string name="home_selinux_status_disabled">Disabled</string>
    <string name="home_selinux_status_enforcing">Enforcing</string>
    <string name="home_selinux_status_permissive">Permissive</string>
    <string name="home_selinux_status_unknown">Unknown</string>

    <string name="home_device_info">Device</string>
    <string name="home_system_version">System version</string>
    <string name="home_kpatch_version">KernelPatch version</string>
    <string name="home_su_path">Executable su</string>
    <string name="home_apatch_version">APatch version</string>

    <string name="kpm">KPModule</string>
    <string name="kpm_kp_not_installed">KernelPatch not installed</string>
    <string name="kpm_add_kpm">Add KPM</string>
    <string name="kpm_load">Load</string>
    <string name="kpm_install">Install</string>
    <string name="kpm_embed">Embed</string>
    <string name="kpm_load_toast_succ">Load succeed</string>
    <string name="kpm_load_toast_failed">Load failed</string>
    <string name="kpm_unload_confirm">Unload \"%s\" module?</string>
    <string name="kpm_unload">Unload</string>
    <string name="kpm_control">Control</string>
    <string name="kpm_apm_empty">No module loaded</string>
    <string name="kpm_version">Version</string>
    <string name="kpm_license">License</string>
    <string name="kpm_author">Author</string>
    <string name="kpm_desc">Desc</string>
    <string name="kpm_args">Args</string>

    <string name="su_title">Superuser</string>
    <string name="su_selinux_via_hook">bypass via hook</string>
    <string name="su_pkg_excluded_label">exclude</string>
    <string name="su_pkg_excluded_setting_title">Exclude modifications</string>
    <string name="su_pkg_excluded_setting_summary">Enabling this option will allow APatch to restore any files modified by the modules of this app.</string>
    <string name="su_show_system_apps">Show system apps</string>
    <string name="su_hide_system_apps">Hide system apps</string>
    <string name="su_refresh">Refresh</string>

    <string name="apm">APModule</string>
    <string name="apm_not_installed">AndroidPatch not installed</string>
    <string name="apm_failed_to_enable">Couldn\'t turn on the \"%s\" module</string>
    <string name="apm_failed_to_disable">Couldn\'t turn off the \"%s\" module</string>
    <string name="apm_empty">No module installed</string>
    <string name="apm_remove">Remove</string>
    <string name="apm_install">Install</string>
    <string name="apm_uninstall_confirm">Uninstall the \"%s\" module?</string>
    <string name="apm_uninstall_success">%s uninstalled</string>
    <string name="apm_uninstall_failed">Couldn\'t uninstall %s</string>
    <string name="apm_version">Version</string>
    <string name="apm_author">Author</string>
    <string name="apm_desc">Desc</string>
    <string name="apm_overlay_fs_not_available">Modules are unavailable as OverlayFS is disabled by the kernel!</string>
    <string name="apm_magisk_conflict">Modules are unavailable due to a conflict with Magisk!</string>
    <string name="apm_reboot_to_apply">Reboot to take effect</string>
    <string name="apm_changelog">Changelog</string>
    <string name="apm_update">Update</string>
    <string name="apm_downloading">Downloading \"%s\" module…</string>
    <string name="apm_start_downloading">Start downloading %s</string>
    <string name="apm_new_version_available">New version %s is available, click to upgrade.</string>

    <string name="hide_apatch_manager">Hide APatch Manager</string>
    <string name="hide_apatch_manager_summary">Install a proxy app with a random package ID and custom app label</string>
    <string name="hide_apatch_dialog_new_manager_name">New manager name</string>
    <string name="hide_apatch_dialog_summary">It will be used as the new app label shown in launcher</string>
    <string name="hide_apatch_manager_failure">Failed to hide. Please report the bug!</string>

    <string name="setting_reset_su_path">Reset su path</string>
    <string name="setting_reset_su_new_path">New full path</string>

    <string name="apm_webui_open">Open</string>
    <string name="apm_action">Action</string>
    <string name="enable_web_debugging">Enable WebView debugging</string>
    <string name="enable_web_debugging_summary">Can be used to debug WebUI. Please enable only when needed.</string>

    <string name="settings_donot_store_superkey">Don\'t store SuperKey in local</string>
    <string name="settings_donot_store_superkey_summary">Authenticate the SuperKey every time the manager starts</string>

    <string name="home_new_apatch_found">New version %s is available, click to upgrade.</string>

    <string name="mode_select_page_title">Install</string>
    <string name="mode_select_page_patch_and_install">Patch and install</string>
    <string name="mode_select_page_select_file">Select a boot image to patch</string>
    <string name="mode_select_page_install_inactive_slot">Install to inactive slot (After OTA)</string>
    <string name="mode_select_page_install_inactive_slot_warning">Your device will be **FORCED** to boot to the current inactive slot after a reboot!\nOnly use this option after OTA is done.\nContinue?</string>

    <string name="home_dialog_auth_fail_title">Authentication failed</string>
    <string name="home_dialog_auth_fail_content">Unable to authenticate the SuperKey, therefore APatch cannot work properly.\nHere are some possible reasons for authentication failure:\n1. You didn\'t patch the boot.img with KernelPatch.\n2. The patched boot.img wasn\'t flashed.\n3. The SuperKey is wrong or contains special characters.\n4. Your device doesn\'t support APatch and KernelPatch.\n\nPlease check and try again. If there are still issues, you can always ask questions on the official repository\'s issues page.</string>

    <string name="home_more_menu_feedback_or_suggestion">Feedback or suggestion</string>
    <string name="home_more_menu_about">About</string>

    <string name="home_dialog_uninstall_title">Uninstall</string>

    <string name="home_dialog_uninstall_ap_only">Remove AndroidPatch</string>
    <string name="home_dialog_uninstall_all">Uninstall all</string>

    <string name="kpm_control_dialog_title">Control KPModule</string>
    <string name="kpm_control_dialog_content">Input control parameters:</string>
    <string name="kpm_control_paramters">Parameters</string>
    <string name="kpm_control_outMsg">Message</string>
    <string name="kpm_control_ok">Operation succeeded!</string>
    <string name="kpm_control_failed">Operation failed!</string>

    <string name="settings_night_mode_follow_sys">Follow system\'s dark theme</string>
    <string name="settings_night_mode_follow_sys_summary">Automatically switch to dark theme based on system settings</string>

    <string name="settings_night_theme_enabled">Enable dark theme</string>

    <string name="settings_use_system_color_theme">System color theme</string>
    <string name="settings_use_system_color_theme_summary">Use a color theme generated by the system based on the current wallpaper</string>

    <string name="settings_custom_color_theme">Color theme</string>
    <string name="amber_theme">Amber</string>
    <string name="blue_grey_theme">Blue grey</string>
    <string name="blue_theme">Blue</string>
    <string name="brown_theme">Brown</string>
    <string name="cyan_theme">Cyan</string>
    <string name="deep_orange_theme">Deep orange</string>
    <string name="deep_purple_theme">Deep purple</string>
    <string name="green_theme">Green</string>
    <string name="indigo_theme">Indigo</string>
    <string name="light_blue_theme">Light blue</string>
    <string name="light_green_theme">Light green</string>
    <string name="lime_theme">Lime</string>
    <string name="orange_theme">Orange</string>
    <string name="pink_theme">Pink</string>
    <string name="purple_theme">Purple</string>
    <string name="red_theme">Red</string>
    <string name="sakura_theme">Sakura</string>
    <string name="teal_theme">Teal</string>
    <string name="yellow_theme">Yellow</string>
    <string name="about_app_version">Version %1$s</string>
    <string name="about_app_desc">Kernel root solution with only a stripped kernel image, providing the ability of mounting system module and hooking kernel function.</string>
    <string name="about_github" translatable="false">GitHub</string>
    <string name="about_weblate" translatable="false">Weblate</string>
    <string name="about_telegram_channel">Telegram channel</string>
    <string name="about_telegram_group">Telegram group</string>
    <string name="about_powered_by">Powered by %1$s</string>
    <string name="crash_handle_title">App crashed…</string>
    <string name="crash_handle_copy">Copy crash log</string>
    <string name="crash_handle_copied">Crash log copied to clipboard.</string>
    <string name="settings_lite_mode">Enable Lite Mode</string>
    <string name="settings_lite_mode_mode_summary">APModule functions may approach damage after enabled, but sometimes it can effectively avoid detection, Reboot required.</string>
    <string name="settings_force_overlayfs_mode">Force using OverlayFS</string>
    <string name="settings_force_overlayfs_mode_summary">Use OverlayFS to mount APModules instead of Magic Mount while kernel supports it, Reboot required.</string>
</resources>
