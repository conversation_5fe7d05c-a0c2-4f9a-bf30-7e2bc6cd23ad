version: 2
updates:
  - package-ecosystem: gradle
    directory: "/"
    schedule:
      interval: daily
    target-branch: main
    registries:
      - maven-google
      - gradle-plugin
    groups:
      maven-dependencies:
        patterns:
          - "*"

  - package-ecosystem: github-actions
    target-branch: main
    directory: /
    schedule:
      interval: daily
    groups:
      action-dependencies:
        patterns:
          - "*"

  - package-ecosystem: cargo
    target-branch: main
    directory: apd/
    schedule:
      interval: daily
    allow:
      - dependency-type: "all"
    groups:
      rust-dependencies:
        patterns:
          - "*"

registries:
  maven-google:
    type: maven-repository
    url: "https://dl.google.com/dl/android/maven2/"
  gradle-plugin:
    type: maven-repository
    url: "https://plugins.gradle.org/m2/"
