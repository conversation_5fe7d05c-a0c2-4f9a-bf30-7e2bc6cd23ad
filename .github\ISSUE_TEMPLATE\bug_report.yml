name: Bug report | 反馈 Bug
description: Report bugs or unexpected behavior | 报告错误或未预料的行为
labels: [bug]

body:
  - type: markdown
    attributes:
      value: |
        Thanks for reporting issues of APatch!
        To better assist you, please provide the following information.
        To avoid duplicate issues, please use English in the title.

        感谢给 APatch 汇报问题！
        为了使我们更好地帮助你，请提供以下信息。
        为了防止重复汇报，标题请务必使用英文。

  - type: checkboxes
    attributes:
       label: Please check before submitting an issue | 在提交 Issue 前请检查
       options:
          - label: I searched the issues and didn't found anything relevant | 我已经搜索了 Issues 列表，没有发现于本问题相关内容
            required: true

          - label: If the patch fails or the image cannot be booted after flashing the new boot.img, visit KernelPatch to clarify your doubts | 修复失败或刷入修补后镜像不能启动，请前往 KernelPatch 提问
            required: true

          - label: I will upload the bug report file in APatch Manager > Settings > Send logs | 我会上传 Bug Report 文件从 APatch 管理器 > 设置 > 发送日志
            required: true

          - label: I know how to reproduce the issue, which might not be specific to my device | 我知道如何重新复现这个问题
            required: false

  - type: checkboxes
    id: latest
    attributes:
      label: Version requirements | 版本要求
      options:
        - label: I'm using the latest CI version of APatch Manager | 我正在使用最新 CI 版本
          required: true

  - type: textarea
    attributes:
        label: Bug description | 描述 Bug
        description: |
          Please enter a clear and concise description of the bug.  
          对 Bug 的清晰简洁的描述。
    validations:
        required: true

  - type: textarea
    attributes:
        label: Reproduce method | 复现方法
        description: |
          Steps to reproduce the bug.  
          复现的步骤。
        placeholder: |
          - 1. Go to...
          - 2. Click on...
          - 3. Scroll down to...
          - 4. See error
    validations:
      required: true

  - type: textarea
    attributes:
        label: Expected behavior | 预期行为
        description: |
          Please enter a clear and concise description of what you expected to happen.  
          对你期望发生的行为进行清晰简洁的描述。
    validations:
      required: true

  - type: textarea
    attributes:
        label: Actual behavior | 实际行为
        description: |
          Tell us what actually happened.  
          告诉我们实际发生了什么。
    validations:
      required: true

  - type: textarea
    attributes:
        label: Screenshots | 截图
        description: |
          If possible, add screenshots to help explain your issue.  
          如果可以的话，添加截图可以帮你解释问题。

  - type: textarea
    attributes:
        label: Logs | 日志
        description: |
          If possible, add the crash log to help us find your issue.  
          如果可以的话，添加崩溃日志可以帮助我们找到问题。

  - type: input
    attributes:
      label: Device name | 设备名称
    validations:
      required: true

  - type: input
    attributes:
      label: OS version | 系统版本
    validations:
      required: true

  - type: input
    attributes:
      label: APatch version | APatch 版本
    validations:
      required: true

  - type: input
    attributes:
      label: Kernel version | 内核版本
    validations:
      required: true

  - type: input
    attributes:
      label: KernelPatch version | KernelPatch 版本
    validations:
      required: true

  - type: textarea
    attributes:
        label: Other information | 其他信息
        description: |
          Add any information about the issue.  
          添加关于问题的任何信息。
        placeholder: |
          Upload logs in .zip format by clicking the bottom bar. Uploading logs to other websites or using external links isn't allowed  
          点击文本框底栏上传日志压缩包，禁止上传到其它网站或使用外链提供日志
    validations:
      required: true
