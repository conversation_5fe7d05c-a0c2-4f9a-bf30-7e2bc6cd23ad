# Часто задаваемые вопросы


## Что такое APatch?
APatch - это root решение, похожее на Magisk или KernelSU, которое объединяет лучшее из обоих.
Оно сочетает удобный и простой метод установки Magisk через `boot.img` с мощными возможностями исправления ядра KernelSU.

## В чем разница между APatch и Magisk?
- Magisk изменяет систему инициализации с помощью патча на RAM-диске вашего загрузочного образа, в то время как APatch вносит изменения непосредственно в ядро.


## APatch против KernelSU
- Для KernelSU требуется исходный код ядра вашего устройства, который не всегда предоставляется OEM-производителем. APatch работает напрямую с вашим исходным `boot.img`.


## APatch против Magisk, KernelSU
- APatch позволяет вам при желании не изменять SELinux. Это означает, что поток приложения может быть рутирован, в libsu и IPC нет необходимости.
- **Kernel Patch Module** предоставляется.


## Что такое Kernel Patch Module?
Некоторый код выполняется в пространстве ядра, аналогично загружаемым модулям ядра (Loadable Kernel Modules, LKM).

Кроме того, KPM предоставляет возможность выполнять inline-hook, syscall-table-hook в пространстве ядра.

Для получения дополнительной информации смотрите [Как написать KPM](https://github.com/bmax121/KernelPatch/blob/main/doc/module.md)


## Связь между APatch и KernelPatch

APatch основан на KernelPatch, унаследовал все его возможности и был расширен.

Вы можете установить только KernelPatch, но это не позволит вам использовать модули Magisk, а чтобы использовать управление суперпользователем, вам необходимо установить AndroidPatch, а затем удалить его.

[Узнать больше о KernelPatch](https://github.com/bmax121/KernelPatch)


## Что такое SuperKey?
KernelPatch добавляет новый системный вызов (syscall) для предоставления всех возможностей приложениям и программам в пользовательском пространстве. Этот системный вызов называется **SuperCall**.
Когда приложение/программа пытается вызвать **SuperCall**, ему необходимо предоставить учетные данные для доступа, называемые **SuperKey**.
**SuperCall** может быть успешно вызван только в том случае, если **SuperKey** правильный, а в противном случае вызывающий объект останется незатронутым.


## Что насчет SELinux?
- KernelPatch не изменяет контекст SELinux и обходит SELinux с помощью перехвата.
  Это позволяет вам рутировать поток Android в контексте приложения без необходимости использовать libsu для запуска нового процесса и последующего выполнения IPC.
  Это очень удобно.
- Кроме того, APatch напрямую использует magiskpolicy для обеспечения дополнительной поддержки SELinux.
