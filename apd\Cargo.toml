[package]
name = "apd"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = "1"
csv = "1.3.1"
clap = { version = "4", features = ["derive"] }
const_format = "0.2"
zip = { version = "4.2.0", default-features = false }
zip-extensions = { git = "https://github.com/AndroidPatch/zip-extensions-rs.git", branch = "master", features = [
    "deflate",
    "lzma",
    "xz",
], default-features = false }
java-properties = { git = "https://github.com/AndroidPatch/java-properties.git", branch = "master", default-features = false }
log = "0.4"
env_logger = "0.11"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
encoding_rs = "0.8"
walkdir="2.4"
retry = "2"
libc = "0.2"
extattr = "1"
jwalk = "0.8"
is_executable = "1"
nom = "8"
derive-new = "0.7.0"
which = "8"
getopts = "0.2"
errno = "0.3.11"
notify = "8.0"

[target.'cfg(any(target_os = "android", target_os = "linux"))'.dependencies]
rustix = { git = "https://github.com/AndroidPatch/rustix", branch = "main", features = ["all-apis"] }
# some android specific dependencies which compiles under unix are also listed here for convenience of coding
android-properties = { version = "0.2.2", features = ["bionic-deprecated"] }
procfs = "0.17"
loopdev = { git = "https://github.com/AndroidPatch/loopdev" }

[target.'cfg(target_os = "android")'.dependencies]
android_logger = { version = "0.15", default-features = false }

[profile.release]
strip = true
opt-level = 3
codegen-units = 1
panic = "abort"
lto = true
