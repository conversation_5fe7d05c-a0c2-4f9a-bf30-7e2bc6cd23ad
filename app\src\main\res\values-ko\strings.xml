<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name" translatable="false">APatch</string>
    <string name="home">홈</string>
    <string name="patch_warnning">설치에는 위험이 따릅니다. 데이터가 백업되었는지 확인하세요.</string>
    <string name="patch">패치</string>
    <string name="kernel_patch">KernelPatch</string>
    <string name="android_patch">AndroidPatch</string>
    <string name="reboot">재시작</string>
    <string name="settings">설정</string>
    <string name="reboot_bootloader">부트로더로 재시작</string>
    <string name="reboot_download">다운로드 모드로 재부팅</string>
    <string name="reboot_edl">EDL로 재시작</string>
    <string name="about">정보</string>
    <string name="settings_app_language">언어</string>
    <string name="home_patch_set_key_desc">KernelPatch용 자격 증명</string>
    <string name="home_patch_next_step">다음 단계</string>
    <string name="home_not_installed">설치되지 않음</string>
    <string name="home_install_unknown">설치되지 않았거나 인증되지 않음</string>
    <string name="home_click_to_install">클릭하여 설치</string>
    <string name="home_working">작동 중 😋</string>
    <string name="home_installing">설치 중</string>
    <string name="home_need_update">새 버전 사용 가능</string>
    <string name="kpatch_version">버전: %s</string>
    <string name="apatch_version">버전: %s</string>
    <string name="apatch_version_update">버전: %s -&gt; %s</string>
    <string name="home_su_path">실행 가능한 su</string>
    <string name="home_apatch_version">APatch 버전</string>
    <string name="home_auth_key_title">SuperKey 입력</string>
    <string name="home_auth_key_desc">인증 후 시작하기</string>
    <string name="home_ap_cando_install">설치</string>
    <string name="home_ap_cando_update">업데이트</string>
    <string name="home_ap_cando_uninstall">제거</string>
    <string name="home_kernel">커널</string>
    <string name="home_manager_version">매니저 앱 버전</string>
    <string name="home_fingerprint">지문</string>
    <string name="home_selinux_status">SELinux 상태</string>
    <string name="home_selinux_status_disabled">끄기</string>
    <string name="home_selinux_status_enforcing">강제</string>
    <string name="home_selinux_status_permissive">허용</string>
    <string name="home_selinux_status_unknown">알 수 없음</string>
    <string name="kpm">KPModule</string>
    <string name="kpm_kp_not_installed">KernelPatch가 설치되지 않았습니다</string>
    <string name="kpm_load">불러오기</string>
    <string name="kpm_load_toast_failed">불러오기 실패</string>
    <string name="kpm_unload">제거</string>
    <string name="kpm_apm_empty">불러온 모듈이 없습니다</string>
    <string name="kpm_version">버전</string>
    <string name="kpm_license">라이선스</string>
    <string name="kpm_author">제작자</string>
    <string name="su_pkg_excluded_setting_summary">이 옵션을 활성화하면 APatch가 이 어플리케이션에 대한 모듈에 의해 수정된 파일을 복원할 수 있습니다.</string>
    <string name="su_show_system_apps">시스템 앱 표시</string>
    <string name="su_hide_system_apps">시스템 앱 숨기기</string>
    <string name="su_refresh">새로 고침</string>
    <string name="apm">APModule</string>
    <string name="apm_failed_to_disable">\"%s\" 모듈을 끌 수 없습니다</string>
    <string name="apm_empty">설치된 모듈이 없습니다</string>
    <string name="apm_remove">제거</string>
    <string name="apm_install">설치</string>
    <string name="apm_uninstall_confirm">\"%s\" 모듈을 제거하시겠습니까?</string>
    <string name="apm_uninstall_success">%s이 제거되었습니다</string>
    <string name="apm_uninstall_failed">%s 을 제거하지 못했습니다</string>
    <string name="apm_version">버전</string>
    <string name="apm_author">제작자</string>
    <string name="apm_overlay_fs_not_available">커널에 의해 OverlayFS가 비활성화되어 모듈을 사용할 수 없습니다.</string>
    <string name="system_default">시스템 기본값</string>
    <string name="home_click_to_learn_apatch">APatch의 기능 및 사용 방법 알아보기</string>
    <string name="about_source_code"><![CDATA[<p>%1$s에서 소스 코드 보기.<p/>%2$s 채널 참여<p/>%3$s 그룹 참여]]></string>
    <string name="safe_mode">안전 모드</string>
    <string name="kpm_load_toast_succ">불러오기 성공</string>
    <string name="home_learn_apatch">APatch 알아보기</string>
    <string name="send_log">로그 보내기</string>
    <string name="super_key">SuperKey</string>
    <string name="clear_super_key">SuperKey 지우기</string>
    <string name="patch_set_superkey">SuperKey 설정</string>
    <string name="patch_title">패치</string>
    <string name="su_title">슈퍼유저</string>
    <string name="reboot_recovery">리커버리로 재시작</string>
    <string name="su_selinux_via_hook">훅으로 우회하기</string>
    <string name="kpm_unload_confirm">\"%s\" 모듈을 제거하시겠습니까?</string>
    <string name="su_pkg_excluded_label">제외</string>
    <string name="su_pkg_excluded_setting_title">변경 제외</string>
    <string name="apm_not_installed">AndroidPatch가 설치되지 않음</string>
    <string name="apm_failed_to_enable">\"%s\" 모듈을 키지 못했습니다</string>
    <string name="apm_magisk_conflict">Magisk와 충돌이 발생해 모듈이 비활성화되었습니다.</string>
    <string name="apm_reboot_to_apply">적용하려면 재시작하세요</string>
    <string name="apm_changelog">변경 기록</string>
    <string name="apm_update">업데이트</string>
    <string name="apm_downloading">\"%s\"모듈 내려받는 중…</string>
    <string name="apm_start_downloading">%s 내려받기를 시작합니다</string>
    <string name="apm_new_version_available">새 버전 %s을 설치하려면 클릭하세요.</string>
    <string name="kpm_desc">설명</string>
    <string name="kpm_args">인수</string>
    <string name="apm_desc">설명</string>
    <string name="settings_global_namespace_mode">글로벌 네임스페이스 모드</string>
    <string name="settings_global_namespace_mode_summary">모든 루트 세션은 글로벌 마운트 세션을 사용합니다</string>
    <string name="settings_check_update">업데이트 확인</string>
    <string name="settings_check_update_summary">앱을 열 때 자동으로 업데이트 확인</string>
    <string name="home_kpatch_info_title">정보</string>
    <string name="kpatch_shadow_path_title">kpatch</string>
    <string name="patch_item_kpimg_comile_time">시간:</string>
    <string name="patch_mode_uninstall_patch">모드: KPatch 제거</string>
    <string name="patch_select_bootimg_btn">boot 선택</string>
    <string name="patch_item_existed_extra_kpm">이미 있음</string>
    <string name="patch_item_extra_name">이름:</string>
    <string name="patch_item_extra_kpm_license">라이선스:</string>
    <string name="patch_item_extra_author">제작자:</string>
    <string name="patch_item_set_skey_label">Super Key의 길이는 숫자와 문자를 포함하여 8자리 이상이여야 합니다</string>
    <string name="kpm_install">설치</string>
    <string name="kpm_embed">통합</string>
    <string name="hide_apatch_dialog_new_manager_name">새 관리자 이름</string>
    <string name="hide_apatch_manager_summary">무작위 패키지 ID와 맞춤 이름으로 관리자 앱 설치하기</string>
    <string name="patch_item_extra_kpm_desciption">설명:</string>
    <string name="home_new_apatch_found">새 버전 사용 가능: %s, 업그레이드하려면 클릭하세요</string>
    <string name="kpatch_version_update">버전: %s -&gt; %s</string>
    <string name="home_more_menu_about">정보</string>
    <string name="home_more_menu_feedback_or_suggestion">피드백 또는 제안</string>
    <string name="kpm_add_kpm">KPM 추가</string>
    <string name="home_install_unknown_summary">클릭하여 설치</string>
    <string name="mode_select_page_install_inactive_slot">비활성 슬롯에 설치 (OTA 이후)</string>
    <string name="mode_select_page_select_file">패치할 boot 이미지 선택</string>
    <string name="home_dialog_uninstall_title">제거</string>
    <string name="home_dialog_uninstall_all">전부 제거</string>
    <string name="patch_config_title">패치</string>
    <string name="patch_mode_patch_and_install">모드: 패치 및 설치</string>
    <string name="patch_item_extra_args">인자:</string>
    <string name="home_device_info">장치</string>
    <string name="home_kpatch_version">KernelPatch 버전</string>
    <string name="hide_apatch_manager">APatch 관리자 숨기기</string>
    <string name="hide_apatch_dialog_summary">런처에서 보여질 이름으로 사용됩니다</string>
    <string name="hide_apatch_manager_failure">숨길 수 없습니다. 버그를 제보해주세요!</string>
    <string name="settings_donot_store_superkey_summary">관리자가 시작할 때마다 SuperKey를 인증합니다</string>
    <string name="mode_select_page_patch_and_install">패치 및 설치</string>
    <string name="mode_select_page_install_inactive_slot_warning">재시작 후 기기가 비활성 슬롯으로 **강제** 부팅될 것 입니다!
\nOTA 이후에만 이 옵션을 사용하세요.
\n계속하시겠습니까?</string>
    <string name="home_dialog_uninstall_ap_only">AndroidPatch 제거</string>
    <string name="home_ap_cando_reboot">재시작</string>
    <string name="patch_mode_bootimg_patch">모드: 패치</string>
    <string name="patch_mode_install_to_next_slot">모드: 다음 를롯에 설치 (OTA 이후)</string>
    <string name="patch_item_kpimg_config">설정:</string>
    <string name="patch_item_new_extra_kpm">새로 통합</string>
    <string name="patch_item_extra_version">버전:</string>
    <string name="patch_item_extra_event">이벤트:</string>
    <string name="patch_item_skey">관리자 키</string>
    <string name="home_system_version">시스템 버전</string>
    <string name="mode_select_page_title">설치</string>
    <string name="home_dialog_auth_fail_title">인증 실패</string>
    <string name="home_dialog_auth_fail_content">SuperKey를 인증할 수 없어 APatch가 제대로 작동할 수 없습니다. \n다음은 인증 실패의 몇 가지 가능한 이유입니다: \n1. KernelPatch로 boot 이미지를 패치하지 않았습니다. \n2. 패치한 boot 이미지를 플래시하지 않았습니다. \n3. SuperKey가 틀림. \n4. 장치가 Apatch와 KernelPatch를 지원하지 않습니다. \n \n확인 후 다시 시도해주세요. 여전히 문제가 있는 경우 공식 repository의 issues 페이지에서 언제든지 질문할 수 있습니다.</string>
    <string name="patch_item_kernel">커널</string>
    <string name="patch_item_kpimg">kpimg</string>
    <string name="patch_item_kpimg_version">버전:</string>
    <string name="patch_embed_kpm_btn">KPM 통합</string>
    <string name="patch_start_patch_btn">시작</string>
    <string name="patch_start_unpatch_btn">패치 해제</string>
    <string name="patch_item_error">!!오류!!</string>
    <string name="patch_item_bootimg">bootimg</string>
    <string name="patch_item_bootimg_slot">슬롯:</string>
    <string name="patch_item_bootimg_dev">장치:</string>
    <string name="setting_reset_su_path">su 경로 초기화</string>
    <string name="apm_webui_open">열기</string>
    <string name="enable_web_debugging">WebView 디버깅 활성화</string>
    <string name="enable_web_debugging_summary">WebUI를 디버그할 때 사용할 수 있습니다. 필요한 경우에만 활성화하세요.</string>
    <string name="settings_donot_store_superkey">기기에 SuperKey 저장하지 않기</string>
    <string name="success">성공</string>
    <string name="failure">실패</string>
    <string name="settings_clear_super_key_dialog">정말로 계속하시겠습니까?</string>
    <string name="kpm_control_dialog_title">KPModule 제어</string>
    <string name="kpm_control_dialog_content">제어 매개 변수 입력:</string>
    <string name="kpm_control">제어</string>
    <string name="kpm_control_paramters">매개변수</string>
    <string name="kpm_control_outMsg">메시지</string>
    <string name="setting_reset_su_new_path">새 전체 경로</string>
    <string name="kpm_control_ok">작업 성공!</string>
    <string name="kpm_control_failed">작업 실패!</string>
    <string name="settings_night_mode_follow_sys">시스템의 어두운 테마 따르기</string>
    <string name="amber_theme">등색 테마</string>
    <string name="settings_use_system_color_theme">시스템 색상 테마</string>
    <string name="settings_use_system_color_theme_summary">시스템이 현재 배경화면으로 생성한 색상 테마 사용</string>
    <string name="settings_custom_color_theme">색상 테마</string>
    <string name="blue_grey_theme">블루그레이 테마</string>
    <string name="blue_theme">블루 테마</string>
    <string name="about_app_desc">제거된 커널 이미지만 있는 커널 루트 솔루션으로, 시스템 모듈을 마운트하고 커널 기능을 후크하는 기능을 제공합니다.</string>
    <string name="about_powered_by">%1$s로 구동됨</string>
    <string name="settings_night_mode_follow_sys_summary">시스템 설정에 따라 자동으로 어두운 테마로 전환</string>
    <string name="settings_night_theme_enabled">어두운 테마 사용</string>
    <string name="brown_theme">브라운 테마</string>
    <string name="cyan_theme">청록색 테마</string>
    <string name="deep_orange_theme">진한 오렌지 테마</string>
    <string name="deep_purple_theme">진한 퍼플 테마</string>
    <string name="green_theme">그린 테마</string>
    <string name="indigo_theme">남색 테마</string>
    <string name="light_blue_theme">라이트 블루 테마</string>
    <string name="light_green_theme">라이트 그린 테마</string>
    <string name="lime_theme">라임 테마</string>
    <string name="orange_theme">오렌지 테마</string>
    <string name="pink_theme">핑크 테마</string>
    <string name="purple_theme">퍼플 테마</string>
    <string name="red_theme">레드 테마</string>
    <string name="sakura_theme">벚꽃 테마</string>
    <string name="teal_theme">청록색 테마</string>
    <string name="yellow_theme">옐로우 테마</string>
    <string name="about_app_version">버전 %1$s</string>
    <string name="about_telegram_channel">Telegram 채널</string>
    <string name="about_telegram_group">Telegram 그룹</string>
    <string name="save_log">로그 저장</string>
    <string name="log_saved">로그 저장됨</string>
    <string name="crash_handle_title">앱 충돌됨...</string>
    <string name="crash_handle_copy">충돌 로그 복사</string>
    <string name="apm_action">동작</string>
    <string name="crash_handle_copied">충돌 로그가 클립보드에 복사됨.</string>
</resources>
