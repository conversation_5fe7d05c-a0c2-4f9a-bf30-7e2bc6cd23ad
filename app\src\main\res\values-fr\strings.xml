<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name" translatable="false">APatch</string>
    <string name="kernel_patch">KernelPatch</string>
    <string name="android_patch">AndroidPatch</string>
    <string name="reboot_recovery">Redémarrer en mode récupération</string>
    <string name="reboot_download">Redémarrer en mode téléchargement</string>
    <string name="reboot_edl">Redémarrer en mode EDL</string>
    <string name="about">À propos</string>
    <string name="home_learn_apatch">En apprendre plus sur AndroidPatch</string>
    <string name="home">Accueil</string>
    <string name="patch_warnning">L\'installation comporte des risques. Veillez à ce que vos données soient sauvegardées.</string>
    <string name="settings">Paramètres</string>
    <string name="reboot_bootloader">Redémarrage en mode bootloader</string>
    <string name="reboot">Redémarrer</string>
    <string name="home_click_to_learn_apatch">Découvrez les fonctionnalités d\'AndroidPatch et apprenez comment l\'utiliser</string>
    <string name="safe_mode">Mode sécurisé</string>
    <string name="home_not_installed">Non installé</string>
    <string name="home_click_to_install">Appuyer pour installer</string>
    <string name="home_installing">Installation</string>
    <string name="home_need_update">Nouvelle version disponible</string>
    <string name="apatch_version">Version : %s</string>
    <string name="home_patch_next_step">Suivant</string>
    <string name="home_install_unknown">Non installé ou authentifié</string>
    <string name="kpatch_version">Version : %s</string>
    <string name="clear_super_key">Effacer la clé</string>
    <string name="home_su_path">Exécutable su</string>
    <string name="home_apatch_version">Version d\'APatch</string>
    <string name="apatch_version_update">Version : %s -&gt; %s</string>
    <string name="about_source_code"><![CDATA[<p>Voir le code source sur %1$s<p/>Rejoindre notre canal %2$s<p/>Rejoindre notre groupe %3$s]]></string>
    <string name="patch_set_superkey">Initialiser la clé</string>
    <string name="send_log">Envoyer le log</string>
    <string name="home_kernel">Noyau</string>
    <string name="kpm">KPModule</string>
    <string name="apm_version">Version</string>
    <string name="home_working">Fonctionnel 😋</string>
    <string name="home_patch_set_key_desc">Authentification unique pour KernelPatch</string>
    <string name="home_auth_key_title">Renseigner la clé</string>
    <string name="home_ap_cando_install">Installer</string>
    <string name="home_ap_cando_update">Mettre à jour</string>
    <string name="home_ap_cando_uninstall">Désinstaller</string>
    <string name="home_manager_version">Version du gestionnaire</string>
    <string name="home_fingerprint">Empreinte</string>
    <string name="kpm_load_toast_failed">Chargement échoué</string>
    <string name="kpm_version">Version</string>
    <string name="kpm_license">Licence</string>
    <string name="kpm_author">Auteur</string>
    <string name="kpm_desc">Description</string>
    <string name="su_title">Super utilisateur</string>
    <string name="su_pkg_excluded_label">exclure</string>
    <string name="su_show_system_apps">Afficher les applications système</string>
    <string name="su_hide_system_apps">Masquer les applications système</string>
    <string name="su_selinux_via_hook">contournement via hook</string>
    <string name="su_refresh">Rafraîchir</string>
    <string name="apm">APModule</string>
    <string name="apm_empty">Aucun module installé</string>
    <string name="apm_install">Installer</string>
    <string name="apm_uninstall_success">%s a été désinstallé</string>
    <string name="apm_author">Auteur</string>
    <string name="apm_desc">Description</string>
    <string name="apm_reboot_to_apply">Redémarrer pour appliquer les changements</string>
    <string name="apm_changelog">Journal des modifications</string>
    <string name="apm_update">Mettre à jour</string>
    <string name="apm_start_downloading">Téléchargement de %s démarré</string>
    <string name="home_selinux_status_disabled">Inactif</string>
    <string name="home_auth_key_desc">Démarrage après certification</string>
    <string name="kpm_unload_confirm">Décharger le module \"%s\" ?</string>
    <string name="apm_not_installed">AndroidPatch n\'est pas installé</string>
    <string name="apm_failed_to_disable">Impossible de désactiver le module \"%s\"</string>
    <string name="apm_uninstall_confirm">Désinstaller le module \"%s\" ?</string>
    <string name="apm_uninstall_failed">Impossible de désinstaller %s</string>
    <string name="apm_magisk_conflict">Les modules sont indisponibles en raison d\'un conflit avec Magisk !</string>
    <string name="apm_downloading">Téléchargement du module \"%s\"…</string>
    <string name="apm_new_version_available">Appuyez pour installer la nouvelle version, %s.</string>
    <string name="home_selinux_status_permissive">Permissif</string>
    <string name="home_selinux_status_unknown">Inconnu</string>
    <string name="super_key">Clé</string>
    <string name="patch_title">Patcher</string>
    <string name="home_selinux_status">État de SELinux</string>
    <string name="home_selinux_status_enforcing">Actif</string>
    <string name="kpm_kp_not_installed">KernelPatch n\'est pas installé</string>
    <string name="kpm_load">Charger</string>
    <string name="kpm_load_toast_succ">Chargement réussi</string>
    <string name="kpm_unload">Décharger</string>
    <string name="kpm_apm_empty">Aucun module chargé</string>
    <string name="apm_failed_to_enable">Impossible d\'activer le module \"%s\"</string>
    <string name="kpm_args">Arguments</string>
    <string name="su_pkg_excluded_setting_summary">L\'activation de cette option permettra à APatch de restaurer tous les fichiers modifiés par les modules de cette application.</string>
    <string name="apm_remove">Désinstaller</string>
    <string name="su_pkg_excluded_setting_title">Exclure les modifications</string>
    <string name="apm_overlay_fs_not_available">Les modules sont indisponibles car OverlayFS est désactivé par le noyau !</string>
    <string name="patch">Patcher</string>
    <string name="settings_app_language">Langue</string>
    <string name="system_default">Système par défaut</string>
    <string name="settings_global_namespace_mode">Mode espace de nommage global</string>
    <string name="settings_global_namespace_mode_summary">Toutes les sessions root utilisent l\'espace de nommage global</string>
    <string name="settings_clear_super_key_dialog">Voulez-vous vraiment continuer ?</string>
    <string name="kpatch_version_update">Version : %s -&gt; %s</string>
    <string name="home_kpatch_info_title">Info</string>
    <string name="home_ap_cando_reboot">Redémarrer</string>
    <string name="patch_config_title">Patchs</string>
    <string name="patch_item_kernel">Noyau</string>
    <string name="patch_item_set_skey_label">La longueur de la clé doit être d\'au moins 8 caractères et inclure à la fois des numéros et des lettres, mais pas de caractères spéciaux.</string>
    <string name="hide_apatch_dialog_new_manager_name">Nouveau nom du gestionnaire</string>
    <string name="hide_apatch_dialog_summary">Il sera utilisé comme label de la nouvelle application affichée dans le lanceur d\'applications</string>
    <string name="hide_apatch_manager_summary">Installer un nouveau gestionnaire avec un identifiant de paquet aléatoire et un label d\'application personnalisé</string>
    <string name="hide_apatch_manager_failure">Échec de la dissimulation, merci de signaler le bug !</string>
    <string name="patch_embed_kpm_btn">Intégrer un KPM</string>
    <string name="patch_start_patch_btn">Démarrer</string>
    <string name="success">Succès</string>
    <string name="failure">Échec</string>
    <string name="patch_mode_bootimg_patch">Mode : Patch</string>
    <string name="patch_select_bootimg_btn">Sélectionner l\'image de boot</string>
    <string name="patch_start_unpatch_btn">Dépatcher</string>
    <string name="patch_item_error">!! ERREUR !!</string>
    <string name="patch_item_bootimg">bootimg</string>
    <string name="patch_item_bootimg_slot">slot :</string>
    <string name="patch_item_bootimg_dev">appareil :</string>
    <string name="patch_item_kpimg">kpimg</string>
    <string name="patch_item_kpimg_version">Version :</string>
    <string name="patch_item_kpimg_comile_time">Date :</string>
    <string name="patch_item_kpimg_config">Config :</string>
    <string name="patch_item_new_extra_kpm">Intégrer un nouveau</string>
    <string name="patch_item_extra_name">Nom :</string>
    <string name="patch_item_extra_author">Auteur :</string>
    <string name="patch_item_extra_kpm_license">Licence :</string>
    <string name="patch_item_extra_kpm_desciption">Description :</string>
    <string name="patch_item_extra_event">Événement :</string>
    <string name="patch_item_skey">Clé</string>
    <string name="kpm_add_kpm">Ajouter un KPM</string>
    <string name="kpm_install">Installer</string>
    <string name="kpm_embed">Intégrer</string>
    <string name="setting_reset_su_path">Réinitialiser le chemin su</string>
    <string name="kpatch_shadow_path_title">kpatch</string>
    <string name="patch_mode_uninstall_patch">Mode : Dépatch</string>
    <string name="patch_item_extra_version">Version :</string>
    <string name="patch_item_extra_args">Args :</string>
    <string name="hide_apatch_manager">Masquer le gestionnaire APatch</string>
    <string name="setting_reset_su_new_path">nouveau chemin complet</string>
    <string name="patch_item_existed_extra_kpm">Existait</string>
    <string name="settings_donot_store_superkey_summary">Authentifier la clé à chaque démarrage du gestionnaire</string>
    <string name="mode_select_page_select_file">Sélectionner une image boot à patcher</string>
    <string name="mode_select_page_patch_and_install">Patcher et installer</string>
    <string name="settings_check_update">Vérifier les mises à jour</string>
    <string name="settings_check_update_summary">Vérifier automatiquement les mises à jour à l\'ouverture de l\'application</string>
    <string name="home_more_menu_about">À propos</string>
    <string name="home_install_unknown_summary">Cliquer pour installer</string>
    <string name="home_device_info">Appareil</string>
    <string name="home_system_version">Version du système</string>
    <string name="home_kpatch_version">Version de KernelPatch</string>
    <string name="enable_web_debugging_summary">Peut être utilisé pour déboguer l\'interface web, n\'activer cette option qu\'en cas de besoin.</string>
    <string name="home_new_apatch_found">Nouvelle version disponible : %s, Cliquer pour mettre à jour.</string>
    <string name="home_dialog_auth_fail_title">Echec de l\'authentification</string>
    <string name="home_dialog_uninstall_title">Désinstaller</string>
    <string name="home_dialog_uninstall_ap_only">Supprimer AndroidPatch</string>
    <string name="home_dialog_uninstall_all">Désinstaller complètement</string>
    <string name="patch_mode_install_to_next_slot">Mode : Installer dans le slot suivant (après OTA)</string>
    <string name="mode_select_page_title">Installer</string>
    <string name="mode_select_page_install_inactive_slot">Installer dans le slot inactif (après OTA)</string>
    <string name="home_more_menu_feedback_or_suggestion">Commentaires ou suggestions</string>
    <string name="patch_mode_patch_and_install">Mode : Patcher et installer</string>
    <string name="mode_select_page_install_inactive_slot_warning">Votre appareil sera **FORCÉ** de démarrer sur le slot inactif actuel après un redémarrage !
\nN\'utilisez cette option qu\'une fois l\'OTA terminée.
\nContinuer ?</string>
    <string name="home_dialog_auth_fail_content">Impossible d\'authentifier la clé, par conséquent APatch ne peut pas fonctionner correctement. \nVoici quelques raisons possibles de l\'échec de l\'authentification : \n1. Vous n\'avez pas patché l\'image de démarrage avec KernelPatch. \n2. L\'image de démarrage patchée n\'a pas été flashée. \n3. La clé est erronée. \n4. Votre appareil ne prend pas en charge APatch et KernelPatch. \n \nVeuillez vérifier et réessayer. Si des problèmes persistent, vous pouvez toujours poser vos questions sur le dépôt officiel.</string>
    <string name="settings_donot_store_superkey">Ne pas stocker la clé localement</string>
    <string name="apm_webui_open">Ouvrir l\'interface web</string>
    <string name="enable_web_debugging">Activer le débogage WebView</string>
    <string name="kpm_control">Contrôle</string>
    <string name="settings_night_mode_follow_sys">Suivre le thème sombre du système</string>
    <string name="teal_theme">Thème sarcelle</string>
    <string name="amber_theme">Thème ambre</string>
    <string name="lime_theme">Thème citron vert</string>
    <string name="pink_theme">Thème rose</string>
    <string name="blue_grey_theme">Thème bleu gris</string>
    <string name="purple_theme">Thème violet</string>
    <string name="brown_theme">Thème marron</string>
    <string name="cyan_theme">Thème cyan</string>
    <string name="orange_theme">Thème orange</string>
    <string name="yellow_theme">Thème jaune</string>
    <string name="about_app_desc">Solution Root noyau avec seulement une image de noyau dépouillée, offrant la possibilité de monter un module système et de hook une fonction du noyau.</string>
    <string name="kpm_control_ok">Opération réussie !</string>
    <string name="settings_night_theme_enabled">Activer le thème sombre</string>
    <string name="settings_use_system_color_theme">Thème de couleur du système</string>
    <string name="about_app_version">Version %1$s</string>
    <string name="kpm_control_dialog_content">Paramètres de contrôle d\'entrée :</string>
    <string name="settings_custom_color_theme">Thème de couleur</string>
    <string name="light_blue_theme">Thème bleu clair</string>
    <string name="kpm_control_dialog_title">Contrôler le KPModule</string>
    <string name="kpm_control_outMsg">Message</string>
    <string name="kpm_control_failed">Opération échouée !</string>
    <string name="settings_use_system_color_theme_summary">Utiliser un thème de couleur généré par le système avec le fond d\'écran actuel</string>
    <string name="blue_theme">Thème bleu</string>
    <string name="deep_orange_theme">Thème orange foncé</string>
    <string name="deep_purple_theme">Thème violet foncé</string>
    <string name="indigo_theme">Thème indigo</string>
    <string name="about_telegram_channel">Canal Telegram</string>
    <string name="about_telegram_group">Groupe Telegram</string>
    <string name="about_powered_by">Propulsé par %1$s</string>
    <string name="green_theme">Thème vert</string>
    <string name="light_green_theme">Thème vert clair</string>
    <string name="sakura_theme">Thème sakura</string>
    <string name="red_theme">Thème rouge</string>
    <string name="kpm_control_paramters">Paramètres</string>
    <string name="settings_night_mode_follow_sys_summary">Basculement automatique vers le thème sombre en fonction des paramètres du système</string>
    <string name="save_log">Enregistrer le journal</string>
    <string name="apm_action">Action</string>
    <string name="log_saved">Journal enregistré</string>
    <string name="crash_handle_title">L\'application a planté…</string>
    <string name="crash_handle_copy">Copier le rapport de plantage</string>
    <string name="crash_handle_copied">Rapport de plantage copié dans le presse-papiers.</string>
</resources>
