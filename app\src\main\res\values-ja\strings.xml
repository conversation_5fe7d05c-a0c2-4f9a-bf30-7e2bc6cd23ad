<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name" translatable="false">APatch</string>
    <string name="home">ホーム</string>
    <string name="android_patch">AndroidPatch</string>
    <string name="kpm_unload_confirm">モジュール \"%s\" をアンロードしますか？</string>
    <string name="home_auth_key_desc">認証後に開始</string>
    <string name="patch_warnning">インストールにはリスクが伴います。データがバックアップされていることを確認してください。</string>
    <string name="home_patch_set_key_desc">KernelPatch の資格情報のみ</string>
    <string name="reboot_download">ダウンロードモードへ再起動</string>
    <string name="home_click_to_learn_apatch">APatch の機能と使い方はこちら</string>
    <string name="reboot_edl">EDL モードへ再起動</string>
    <string name="home_learn_apatch">APatch について</string>
    <string name="kernel_patch">KernelPatch</string>
    <string name="home_need_update">新しいバージョンが利用可能</string>
    <string name="settings">設定</string>
    <string name="about_source_code"><![CDATA[<p>%1$s でソースコードを表示<p>%2$s チャンネルに参加する<p/>%3$s グループに参加する]]></string>
    <string name="home_manager_version">アプリのバージョン</string>
    <string name="reboot">再起動</string>
    <string name="reboot_recovery">リカバリーへ再起動</string>
    <string name="reboot_bootloader">ブートローダーへ再起動</string>
    <string name="safe_mode">セーフモード</string>
    <string name="about">APatch について</string>
    <string name="home_patch_next_step">次へ</string>
    <string name="home_not_installed">未インストール</string>
    <string name="send_log">ログを送信</string>
    <string name="super_key">SuperKey</string>
    <string name="patch_set_superkey">SuperKey を設定</string>
    <string name="home_installing">インストール中</string>
    <string name="home_auth_key_title">SuperKey を入力</string>
    <string name="kpm_kp_not_installed">KernelPatch はインストールされていません</string>
    <string name="kpm_load">ロード</string>
    <string name="kpm_author">著者</string>
    <string name="kpm_desc">説明</string>
    <string name="su_title">スーパーユーザー</string>
    <string name="kpm_license">ライセンス</string>
    <string name="su_pkg_excluded_label">除外</string>
    <string name="apm_version">バージョン</string>
    <string name="apm_update">アップデート</string>
    <string name="home_install_unknown">不明</string>
    <string name="home_click_to_install">タップしてインストール</string>
    <string name="home_working">動作中 😋</string>
    <string name="home_ap_cando_install">インストール</string>
    <string name="home_ap_cando_update">アップデート</string>
    <string name="home_ap_cando_uninstall">アンインストール</string>
    <string name="home_kernel">カーネル</string>
    <string name="home_fingerprint">Fingerprint</string>
    <string name="home_selinux_status">SELinux の状態</string>
    <string name="home_selinux_status_unknown">Unknown</string>
    <string name="kpm_load_toast_succ">ロード成功</string>
    <string name="kpm_load_toast_failed">ロード失敗</string>
    <string name="kpm_version">バージョン</string>
    <string name="su_pkg_excluded_setting_summary">このオプションを有効にすると、APatch はこのアプリのモジュールによって変更されたすべてのファイルを復元できるようになります。</string>
    <string name="su_show_system_apps">システムアプリを表示</string>
    <string name="su_refresh">更新</string>
    <string name="apm_not_installed">AndroidPatch はインストールされていません</string>
    <string name="apm_remove">アンインストール</string>
    <string name="apm_install">インストール</string>
    <string name="apm_author">制作者</string>
    <string name="apm_desc">説明</string>
    <string name="apm_overlay_fs_not_available">カーネルによって OverlayFS が無効なため、モジュールを使用できません。</string>
    <string name="apm_reboot_to_apply">再起動して適用する</string>
    <string name="apm_changelog">変更履歴</string>
    <string name="kpatch_version">バージョン: %s</string>
    <string name="apatch_version">バージョン: %s</string>
    <string name="apatch_version_update">バージョン: %s → %s</string>
    <string name="home_su_path">su</string>
    <string name="home_selinux_status_disabled">無効</string>
    <string name="clear_super_key">SuperKey を消去</string>
    <string name="patch_title">パッチ</string>
    <string name="home_selinux_status_enforcing">Enforcing</string>
    <string name="su_pkg_excluded_setting_title">変更の除外</string>
    <string name="su_hide_system_apps">システムアプリを非表示</string>
    <string name="home_apatch_version">APatch のバージョン</string>
    <string name="home_selinux_status_permissive">Permissive</string>
    <string name="patch">パッチ</string>
    <string name="settings_app_language">言語</string>
    <string name="system_default">システムのデフォルト</string>
    <string name="settings_global_namespace_mode">グローバル名前空間モード</string>
    <string name="settings_global_namespace_mode_summary">すべての root セッションはグローバルマウント名前空間を使用します</string>
    <string name="kpm_args">パラメータ</string>
    <string name="kpm_apm_empty">ロードされたモジュールはありません</string>
    <string name="kpm_unload">アンロード</string>
    <string name="apm_empty">インストールされたモジュールはありません</string>
    <string name="settings_clear_super_key_dialog">本当に続行しますか？</string>
    <string name="kpatch_version_update">バージョン: %s → %s</string>
    <string name="kpatch_shadow_path_title">kpatch</string>
    <string name="home_ap_cando_reboot">再起動</string>
    <string name="patch_config_title">パッチ</string>
    <string name="patch_mode_bootimg_patch">モード: パッチ</string>
    <string name="patch_mode_patch_and_install">モード: パッチしてインストール</string>
    <string name="patch_mode_uninstall_patch">モード: KPatch をアンインストール</string>
    <string name="patch_select_bootimg_btn">boot.img を選択</string>
    <string name="patch_start_unpatch_btn">パッチ解除</string>
    <string name="patch_embed_kpm_btn">KPM を組み込む</string>
    <string name="patch_start_patch_btn">開始</string>
    <string name="kpm">KPModule</string>
    <string name="apm">APModule</string>
    <string name="apm_uninstall_confirm">\"%s\" モジュールをアンインストールしますか？</string>
    <string name="apm_uninstall_failed">%s をアンインストールできませんでした</string>
    <string name="apm_failed_to_disable">\"%s\" モジュールをオフにできませんでした</string>
    <string name="apm_new_version_available">タップして最新バージョン %s をインストール</string>
    <string name="su_selinux_via_hook">フックによるバイパス</string>
    <string name="home_kpatch_info_title">情報</string>
    <string name="patch_item_error">!!エラー!!</string>
    <string name="patch_item_bootimg">bootimg</string>
    <string name="patch_item_bootimg_dev">デバイス:</string>
    <string name="patch_item_kernel">カーネル</string>
    <string name="patch_item_kpimg">kpimg</string>
    <string name="patch_item_kpimg_version">バージョン:</string>
    <string name="patch_item_bootimg_slot">スロット:</string>
    <string name="patch_item_extra_args">引数:</string>
    <string name="patch_item_extra_name">名前:</string>
    <string name="patch_item_extra_version">バージョン:</string>
    <string name="patch_item_new_extra_kpm">新たに組み込む</string>
    <string name="patch_item_existed_extra_kpm">設定済</string>
    <string name="patch_item_extra_event">イベント:</string>
    <string name="patch_item_skey">SuperKey</string>
    <string name="patch_item_extra_author">制作者:</string>
    <string name="patch_item_extra_kpm_desciption">説明:</string>
    <string name="patch_item_set_skey_label">SuperKey の長さは 8 ～ 63 文字で、数字と文字を含めることができますが、特殊文字は使用できません。</string>
    <string name="apm_failed_to_enable">\"%s\" モジュールをオンにできませんでした</string>
    <string name="apm_uninstall_success">%s をアンインストールしました</string>
    <string name="apm_start_downloading">%s のダウンロードを開始</string>
    <string name="apm_downloading">\"%s\" モジュールをダウンロード中…</string>
    <string name="apm_magisk_conflict">Magisk と競合したためモジュールを使用できません。</string>
    <string name="patch_item_kpimg_comile_time">時間:</string>
    <string name="patch_item_kpimg_config">構成:</string>
    <string name="patch_item_extra_kpm_license">ライセンス:</string>
    <string name="settings_check_update">アップデートを確認</string>
    <string name="hide_apatch_manager_summary">ランダムなパッケージ ID とカスタムアプリ名を使用して新しいマネージャーアプリをインストールする</string>
    <string name="hide_apatch_manager">APatchマネージャーを隠す</string>
    <string name="settings_check_update_summary">アプリを開いたときに自動でアップデートを確認する</string>
    <string name="kpm_install">インストール</string>
    <string name="kpm_embed">埋め込む</string>
    <string name="hide_apatch_manager_failure">アプリを隠すことに失敗しました。バグを報告してください</string>
    <string name="home_install_unknown_summary">タップしてインストール</string>
    <string name="setting_reset_su_path">su パスをリセット</string>
    <string name="apm_webui_open">開く</string>
    <string name="patch_mode_install_to_next_slot">モード: 非アクティブ スロットにインストール (OTA 後)</string>
    <string name="home_device_info">デバイス</string>
    <string name="home_system_version">システムのバージョン</string>
    <string name="home_kpatch_version">KernelPatch バージョン</string>
    <string name="success">成功</string>
    <string name="failure">失敗</string>
    <string name="hide_apatch_dialog_summary">これはランチャーに表示される新しいアプリ名として使用されます</string>
    <string name="home_dialog_uninstall_all">全てアンインストール</string>
    <string name="kpm_add_kpm">KPM を追加</string>
    <string name="kpm_control">コントロール</string>
    <string name="hide_apatch_dialog_new_manager_name">新しいマネージャー名</string>
    <string name="setting_reset_su_new_path">新しいフルパス</string>
    <string name="enable_web_debugging">WebView デバッグを有効</string>
    <string name="settings_donot_store_superkey_summary">マネージャーの起動時に SuperKey を認証します</string>
    <string name="home_new_apatch_found">新バージョンが利用可能です: %s タップしてアップデート</string>
    <string name="mode_select_page_title">インストール</string>
    <string name="enable_web_debugging_summary">Web UI のデバッグに使用できます。必要な場合のみ有効にしてください。</string>
    <string name="mode_select_page_patch_and_install">パッチをしてインストール</string>
    <string name="mode_select_page_install_inactive_slot_warning">デバイスは再起動後、**強制的に**アクティブでないスロットにブートされます。
\nこのオプションは、OTA が完了した後のみ使用してください。
\n続行しますか？</string>
    <string name="home_dialog_auth_fail_content">SuperKey の認証に失敗したため、APatchは正しく動作しません。 \n以下の理由が考えられます: \n1. KernelPatch で\"boot.img\"をパッチしていない。 \n2. パッチを適用した\"boot.img\"をフラッシュしていない。 \n3. SuperKey が間違っている。 \n4. デバイスが APatch と KernelPatch に対応していない。 \n \n再度確認して、もう一度お試しください。問題が解決しない場合は、公式リポジトリの Issue ページで質問をして下さい。</string>
    <string name="home_dialog_uninstall_ap_only">APatch をアンインストール</string>
    <string name="mode_select_page_select_file">パッチするブートイメージを選択</string>
    <string name="mode_select_page_install_inactive_slot">非アクティブなスロットにインストール (OTA 後)</string>
    <string name="home_dialog_auth_fail_title">認証失敗</string>
    <string name="home_more_menu_feedback_or_suggestion">フィードバック / 提案</string>
    <string name="home_dialog_uninstall_title">アンインストール</string>
    <string name="kpm_control_dialog_title">KPModule の管理</string>
    <string name="kpm_control_paramters">パラメータ</string>
    <string name="settings_night_theme_enabled">ダークテーマを有効化</string>
    <string name="blue_theme">ブルー</string>
    <string name="brown_theme">ブラウン</string>
    <string name="deep_orange_theme">ディープオレンジ</string>
    <string name="cyan_theme">シアン</string>
    <string name="deep_purple_theme">ディープパープル</string>
    <string name="light_green_theme">ライトグリーン</string>
    <string name="lime_theme">ライム</string>
    <string name="indigo_theme">インディゴ</string>
    <string name="light_blue_theme">ライトブルー</string>
    <string name="purple_theme">パープル</string>
    <string name="save_log">ログを保存</string>
    <string name="settings_donot_store_superkey">ローカルにスーパーキーを保存しない</string>
    <string name="home_more_menu_about">APatch について</string>
    <string name="settings_use_system_color_theme">ダイナミックテーマ</string>
    <string name="settings_night_mode_follow_sys">システムテーマに従う</string>
    <string name="settings_custom_color_theme">カラーテーマ</string>
    <string name="amber_theme">アンバー</string>
    <string name="blue_grey_theme">ブルーグレー</string>
    <string name="green_theme">グリーン</string>
    <string name="orange_theme">オレンジ</string>
    <string name="pink_theme">ピンク</string>
    <string name="red_theme">レッド</string>
    <string name="sakura_theme">サクラ</string>
    <string name="teal_theme">ティール</string>
    <string name="yellow_theme">イエロー</string>
    <string name="about_app_version">バージョン %1$s</string>
    <string name="about_telegram_channel">Telegram チャンネル</string>
    <string name="about_telegram_group">Telegram グループ</string>
    <string name="about_powered_by">Powered by %1$s</string>
    <string name="kpm_control_ok">操作は成功しました！</string>
    <string name="kpm_control_outMsg">メッセージ</string>
    <string name="kpm_control_failed">操作は失敗しました！</string>
    <string name="settings_night_mode_follow_sys_summary">システム設定を元に自動でダークテーマに切り替える</string>
    <string name="settings_use_system_color_theme_summary">現在設定している壁紙からシステムによって生成されたカラーテーマを使用する</string>
    <string name="about_app_desc">削除されたカーネルイメージのみを持つカーネルルートソリューションは、システムモジュールをマウントし、カーネル機能をフックする機能を提供します。</string>
    <string name="kpm_control_dialog_content">入力制御変数:</string>
    <string name="apm_action">アクション</string>
    <string name="log_saved">ログを保存しました</string>
    <string name="crash_handle_copy">クラッシュログをコピー</string>
    <string name="crash_handle_copied">クラッシュ ログをクリップボードにコピーしました。</string>
    <string name="crash_handle_title">アプリがクラッシュしました。</string>
</resources>
