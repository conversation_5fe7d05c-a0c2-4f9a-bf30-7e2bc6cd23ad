<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="apm">系统模块</string>
    <string name="super_key">超级密钥</string>
    <string name="app_name" translatable="false">APatch</string>
    <string name="kpm">内核模块</string>
    <string name="android_patch">系统补丁</string>
    <string name="save_log">保存日志</string>
    <string name="log_saved">日志已保存</string>
    <string name="send_log">发送日志</string>
    <string name="patch_warnning">安装过程存在风险，请确保您的数据已备份。</string>
    <string name="home_patch_set_key_desc">内核补丁的唯一授权凭证</string>
    <string name="home_install_unknown">未安装或未鉴权</string>
    <string name="home_install_unknown_summary">点此安装</string>
    <string name="reboot_download">重启至 Download</string>
    <string name="home_click_to_learn_apatch">查看 APatch 的特性和用法</string>
    <string name="reboot_edl">重启至 EDL</string>
    <string name="home_installing">安装中</string>
    <string name="safe_mode">安全模式</string>
    <string name="reboot_recovery">重启至 Recovery</string>
    <string name="home_learn_apatch">了解 APatch</string>
    <string name="kernel_patch">内核补丁</string>
    <string name="home_click_to_install">点此安装</string>
    <string name="about">关于</string>
    <string name="reboot">重启</string>
    <string name="home_need_update">新版本可升级</string>
    <string name="home">主页</string>
    <string name="patch_set_superkey">设置超级密钥</string>
    <string name="settings">设置</string>
    <string name="home_working">工作中 😋</string>
    <string name="home_patch_next_step">下一步</string>
    <string name="home_not_installed">未安装</string>
    <string name="reboot_bootloader">重启至 Bootloader</string>
    <string name="about_source_code"><![CDATA[<p>在%1$s查看源码<p/>加入我们的%2$s频道<p/>加入我们的%3$s讨论组]]></string>
    <string name="home_auth_key_desc">通过验证后开始使用</string>
    <string name="kpatch_version">版本：%s</string>
    <string name="home_ap_cando_uninstall">卸载</string>
    <string name="apatch_version">版本：%s</string>
    <string name="home_ap_cando_install">安装</string>
    <string name="home_apatch_version">APatch 版本</string>
    <string name="home_su_path">可执行su路径</string>
    <string name="home_ap_cando_update">更新</string>
    <string name="apatch_version_update">版本：%s → %s</string>
    <string name="home_auth_key_title">输入超级密钥</string>
    <string name="home_kernel">内核版本</string>
    <string name="patch_title">修补</string>
    <string name="home_fingerprint">指纹</string>
    <string name="home_selinux_status">SELinux 状态</string>
    <string name="home_manager_version">管理器版本</string>
    <string name="kpm_unload_confirm">卸载“%s”模块？</string>
    <string name="kpm_kp_not_installed">未安装内核补丁</string>
    <string name="kpm_version">版本</string>
    <string name="home_selinux_status_unknown">未知</string>
    <string name="home_selinux_status_permissive">宽容模式</string>
    <string name="kpm_author">作者</string>
    <string name="kpm_load_toast_failed">加载失败</string>
    <string name="kpm_unload">卸载</string>
    <string name="kpm_control">参数</string>
    <string name="home_selinux_status_disabled">禁用</string>
    <string name="kpm_load">加载</string>
    <string name="home_selinux_status_enforcing">强制模式</string>
    <string name="kpm_license">许可</string>
    <string name="kpm_apm_empty">尚未加载模块</string>
    <string name="kpm_load_toast_succ">加载成功</string>
    <string name="su_pkg_excluded_setting_title">排除修改</string>
    <string name="su_pkg_excluded_setting_summary">针对此应用还原被模块改动的文件。</string>
    <string name="su_pkg_excluded_label">排除模块</string>
    <string name="su_title">超级用户</string>
    <string name="kpm_desc">描述</string>
    <string name="su_selinux_via_hook">劫持绕过</string>
    <string name="kpm_args">参数</string>
    <string name="su_refresh">刷新</string>
    <string name="su_show_system_apps">显示系统应用</string>
    <string name="apm_failed_to_enable">无法启用“%s”模块</string>
    <string name="su_hide_system_apps">隐藏系统应用</string>
    <string name="apm_failed_to_disable">无法禁用“%s”模块</string>
    <string name="apm_empty">尚未安装模块</string>
    <string name="apm_reboot_to_apply">重启生效</string>
    <string name="apm_uninstall_success">%s 已卸载</string>
    <string name="apm_install">安装</string>
    <string name="apm_remove">卸载</string>
    <string name="apm_uninstall_confirm">确认要卸载“%s”模块吗？</string>
    <string name="apm_version">版本</string>
    <string name="apm_start_downloading">开始下载 %s</string>
    <string name="apm_overlay_fs_not_available">模块功能不可用，因为内核禁用了 OverlayFS！</string>
    <string name="apm_changelog">更新日志</string>
    <string name="apm_uninstall_failed">无法卸载 %s</string>
    <string name="apm_downloading">正在下载“%s”模块……</string>
    <string name="apm_magisk_conflict">模块功能不可用，因与 Magisk 存在冲突！</string>
    <string name="apm_desc">描述</string>
    <string name="apm_new_version_available">有新版本 %s 可用，单击升级。</string>
    <string name="apm_update">更新</string>
    <string name="apm_author">作者</string>
    <string name="clear_super_key">清除超级密钥</string>
    <string name="apm_not_installed">尚未注入系统补丁</string>
    <string name="system_default">系统默认</string>
    <string name="settings_app_language">语言</string>
    <string name="patch">修补内核镜像</string>
    <string name="settings_global_namespace_mode_summary">所有 Root 会话均使用全局挂载命名空间</string>
    <string name="settings_global_namespace_mode">全局命名空间模式</string>
    <string name="settings_clear_super_key_dialog">你真的确定要继续吗？</string>
    <string name="kpatch_version_update">版本：%s → %s</string>
    <string name="kpatch_shadow_path_title">内核补丁</string>
    <string name="home_kpatch_info_title">信息</string>
    <string name="home_ap_cando_reboot">重启</string>
    <string name="patch_config_title">修补内核</string>
    <string name="patch_mode_bootimg_patch">修补指定内核镜像</string>
    <string name="patch_mode_patch_and_install">修补当前槽位内核</string>
    <string name="patch_select_bootimg_btn">选择 Boot 镜像</string>
    <string name="patch_start_patch_btn">开始修补</string>
    <string name="patch_item_error">‼错误‼</string>
    <string name="patch_item_bootimg">启动镜像</string>
    <string name="patch_item_bootimg_slot">槽位：</string>
    <string name="patch_item_bootimg_dev">设备：</string>
    <string name="patch_item_kernel">内核版本</string>
    <string name="patch_item_kpimg">内核补丁</string>
    <string name="patch_item_kpimg_version">版本：</string>
    <string name="patch_item_kpimg_comile_time">时间：</string>
    <string name="patch_item_kpimg_config">配置：</string>
    <string name="patch_item_new_extra_kpm">嵌入新的</string>
    <string name="patch_embed_kpm_btn">嵌入模块</string>
    <string name="patch_item_extra_name">名称:</string>
    <string name="patch_item_extra_version">版本：</string>
    <string name="patch_item_extra_author">作者：</string>
    <string name="patch_item_extra_kpm_license">许可：</string>
    <string name="patch_item_extra_kpm_desciption">描述：</string>
    <string name="patch_item_extra_args">参数：</string>
    <string name="patch_item_extra_event">事件:</string>
    <string name="patch_item_skey">超级密钥</string>
    <string name="patch_item_existed_extra_kpm">已嵌入的</string>
    <string name="patch_item_set_skey_label">超级密钥长度应在 8-63 个字符，包含数字和字母，但不包含特殊字符。</string>
    <string name="patch_mode_uninstall_patch">还原初始内核镜像</string>
    <string name="patch_start_unpatch_btn">取消修补</string>
    <string name="hide_apatch_dialog_new_manager_name">新管理器名称</string>
    <string name="hide_apatch_dialog_summary">将使用新的名称安装管理器</string>
    <string name="hide_apatch_manager_failure">隐藏失败，请提交反馈！</string>
    <string name="hide_apatch_manager_summary">安装代理应用，安装包 ID 随机且自定义应用标签</string>
    <string name="hide_apatch_manager">隐藏 APatch 管理器</string>
    <string name="kpm_embed">嵌入</string>
    <string name="kpm_add_kpm">添加内核模块</string>
    <string name="kpm_install">安装</string>
    <string name="setting_reset_su_path">重设 SU 路径</string>
    <string name="setting_reset_su_new_path">新的完整路径</string>
    <string name="success">成功</string>
    <string name="failure">失败</string>
    <string name="mode_select_page_title">安装</string>
    <string name="mode_select_page_patch_and_install">直接安装（通常选择此项）</string>
    <string name="mode_select_page_select_file">选取要修补的启动映像文件</string>
    <string name="mode_select_page_install_inactive_slot">修补另一槽位内核（用于 OTA 之后）</string>
    <string name="mode_select_page_install_inactive_slot_warning">将在重启后强制切换到另一个槽位！
\n注意只能在 OTA 更新完成后的重启之前使用。
\n确定要继续吗？</string>
    <string name="home_dialog_auth_fail_title">超级密钥鉴权失败</string>
    <string name="home_dialog_auth_fail_content">超级密钥验证失败，因此 APatch 不能正常工作。\n下面是一些可能的原因： \n⒈ 未使用 KernelPatch 修补内核镜像 \n⒉ KernelPatch 修补后的内核镜像未刷入 \n⒊超级密钥输入错误或包含特殊字符\n⒋设备不支持 APatch 以及 KernelPatch \n\n请检查后重试，如果问题仍然存在，可随时前往 GitHub 仓库的 issues 页面提问。</string>
    <string name="home_more_menu_feedback_or_suggestion">反馈或建议</string>
    <string name="home_more_menu_about">关于</string>
    <string name="settings_check_update">检查更新</string>
    <string name="patch_mode_install_to_next_slot">修补另一槽位内核</string>
    <string name="apm_webui_open">打开</string>
    <string name="enable_web_debugging">启用 WebView 调试</string>
    <string name="home_new_apatch_found">发现新版本：%s，单击升级。</string>
    <string name="settings_check_update_summary">应用启动时自动检测更新</string>
    <string name="enable_web_debugging_summary">可用于调试 WebUI，请仅在需要时启用。</string>
    <string name="settings_donot_store_superkey">不存储超级密钥</string>
    <string name="settings_donot_store_superkey_summary">每次打开管理器时验证超级密钥</string>
    <string name="home_device_info">设备</string>
    <string name="home_system_version">系统版本</string>
    <string name="home_kpatch_version">KernelPatch 版本</string>
    <string name="home_dialog_uninstall_title">卸载</string>
    <string name="home_dialog_uninstall_ap_only">仅卸载系统补丁</string>
    <string name="home_dialog_uninstall_all">完全卸载</string>
    <string name="kpm_control_dialog_title">调节内核模块</string>
    <string name="kpm_control_dialog_content">请输入调控参数：</string>
    <string name="kpm_control_paramters">控制参数</string>
    <string name="kpm_control_outMsg">输出信息</string>
    <string name="kpm_control_ok">操作成功！</string>
    <string name="kpm_control_failed">操作失败！</string>
    <string name="settings_night_mode_follow_sys">深色模式跟随系统</string>
    <string name="settings_night_mode_follow_sys_summary">根据系统的深色模式自动切换</string>
    <string name="settings_night_theme_enabled">深色主题</string>
    <string name="settings_use_system_color_theme">系统颜色主题</string>
    <string name="settings_use_system_color_theme_summary">使用由系统由壁纸取色生成的主题</string>
    <string name="settings_custom_color_theme">颜色主题</string>
    <string name="amber_theme">琥珀色主题</string>
    <string name="blue_theme">蓝色主题</string>
    <string name="blue_grey_theme">蓝灰色主题</string>
    <string name="brown_theme">棕色主题</string>
    <string name="cyan_theme">青蓝色主题</string>
    <string name="deep_orange_theme">深橙色主题</string>
    <string name="deep_purple_theme">深紫色主题</string>
    <string name="green_theme">绿色主题</string>
    <string name="indigo_theme">靛蓝色主题</string>
    <string name="light_blue_theme">浅蓝色主题</string>
    <string name="light_green_theme">浅绿色主题</string>
    <string name="lime_theme">柠檬色主题</string>
    <string name="orange_theme">橙色主题</string>
    <string name="pink_theme">粉红色主题</string>
    <string name="purple_theme">紫色主题</string>
    <string name="red_theme">红色主题</string>
    <string name="sakura_theme">樱花色主题</string>
    <string name="teal_theme">青绿色主题</string>
    <string name="yellow_theme">黄色主题</string>
    <string name="about_app_version">版本 %1$s</string>
    <string name="about_app_desc">内核级 ROOT 解决方案且无需重新编译内核，同时提供挂载系统模块和 Hook 内核函数的能力。</string>
    <string name="about_github" translatable="false">GitHub</string>
    <string name="about_weblate" translatable="false">Weblate</string>
    <string name="about_telegram_channel">Telegram 频道</string>
    <string name="about_telegram_group">Telegram 群组</string>
    <string name="about_powered_by">由 %1$s 强力驱动</string>
    <string name="apm_action">执行</string>
    <string name="crash_handle_title">应用崩溃了…</string>
    <string name="crash_handle_copy">复制崩溃日志</string>
    <string name="crash_handle_copied">崩溃日志已复制到剪贴板。</string>
    <string name="settings_lite_mode">启动 Lite 模式</string>
    <string name="settings_lite_mode_mode_summary">开启后会使系统模块功能接近破损，但有时可以有效地规避检测，重启生效。</string>
    <string name="settings_force_overlayfs_mode">强制使用 OverlayFS</string>
    <string name="settings_force_overlayfs_mode_summary">在内核支持的前提下使用 OverlayFS 挂载系统模块而非 Magic Mount，重启生效。</string>
</resources>
