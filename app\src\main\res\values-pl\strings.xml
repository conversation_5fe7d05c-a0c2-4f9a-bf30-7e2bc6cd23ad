<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="apm">Moduły AP</string>
    <string name="super_key">SuperKey</string>
    <string name="app_name" translatable="false">APatch</string>
    <string name="kpm">Moduły KP</string>
    <string name="home">Pulpit</string>
    <string name="send_log">Prześlij logi</string>
    <string name="kpatch_version">Wersja: %s</string>
    <string name="home_click_to_learn_apatch"><PERSON><PERSON><PERSON> się o funkcjach APatcha i jak go używać</string>
    <string name="home_installing">Instalowanie</string>
    <string name="home_learn_apatch">Poznaj APatch</string>
    <string name="home_click_to_install">Kliknij, aby zainstalować</string>
    <string name="about">Informacje</string>
    <string name="reboot">Uruchom ponownie</string>
    <string name="home_need_update">Dostępna nowa wersja</string>
    <string name="settings">Ustawienia</string>
    <string name="home_patch_next_step">Kolejny krok</string>
    <string name="home_not_installed">Nie zainstalowany</string>
    <string name="about_source_code"><![CDATA[<p>Zobacz kod źródłowy na %1$s<p/>Dołącz do naszego kanału %2$s <p/>Dołącz do naszej grupy %3$s]]></string>
    <string name="home_ap_cando_uninstall">Odinstaluj</string>
    <string name="apatch_version">Wersja: %s</string>
    <string name="home_ap_cando_install">Instaluj</string>
    <string name="home_selinux_status">Status SELinux</string>
    <string name="home_su_path">Plik wykonywalny su</string>
    <string name="apatch_version_update">Wersja: %s -&gt; %s</string>
    <string name="home_kernel">Wersja jądra</string>
    <string name="home_manager_version">Wersja menedżera</string>
    <string name="patch_title">Załataj</string>
    <string name="home_selinux_status_disabled">Wyłączony</string>
    <string name="home_selinux_status_enforcing">Wymuszony</string>
    <string name="su_pkg_excluded_setting_summary">Włączenie tej opcji umożliwi APatch przywrócenie wszystkich plików zmodyfikowanych przez moduły tej aplikacji.</string>
    <string name="su_title">Superuser</string>
    <string name="kpm_version">Wersja</string>
    <string name="su_refresh">Odśwież</string>
    <string name="su_show_system_apps">Pokaż aplikacje systemowe</string>
    <string name="home_selinux_status_permissive">Pobłażliwy</string>
    <string name="kpm_author">Autor</string>
    <string name="su_hide_system_apps">Ukryj aplikacje systemowe</string>
    <string name="kpm_load_toast_failed">Załadowanie nie powiodło się</string>
    <string name="kpm_load">Załaduj</string>
    <string name="kpm_license">Licencja</string>
    <string name="kpm_apm_empty">Brak załadowanych modułów</string>
    <string name="kpm_load_toast_succ">Pomyślnie załadowano</string>
    <string name="apm_uninstall_success">Moduł %s odinstalowany</string>
    <string name="apm_install">Instaluj</string>
    <string name="apm_remove">Usuń</string>
    <string name="apm_uninstall_confirm">Odinstalować moduł \"%s\"?</string>
    <string name="apm_version">Wersja</string>
    <string name="apm_overlay_fs_not_available">Moduły są niedostępne, ponieważ OverlayFS jest wyłączony przez jądro!</string>
    <string name="apm_failed_to_disable">Nie udało się wyłączyć modułu \"%s\"</string>
    <string name="apm_uninstall_failed">Nie udało się odinstalować modułu %s</string>
    <string name="apm_failed_to_enable">Nie udało się włączyć modułu: \"%s\"</string>
    <string name="apm_magisk_conflict">Moduły są niedostępne, ponieważ istnieje konflikt z Magiskiem!</string>
    <string name="apm_author">Autor</string>
    <string name="apm_changelog">Lista zmian</string>
    <string name="apm_downloading">Pobieranie modułu \"%s\"…</string>
    <string name="apm_update">Aktualizacja</string>
    <string name="android_patch">AndroidPatch</string>
    <string name="apm_start_downloading">Rozpocznij pobieranie: %s</string>
    <string name="kernel_patch">KernelPatch</string>
    <string name="apm_new_version_available">Dostępna jest nowa wersja %s. Kliknij, aby uaktualnić.</string>
    <string name="reboot_download">Restartuj do Download</string>
    <string name="reboot_edl">Restartuj do EDL</string>
    <string name="reboot_recovery">Restartuj do Recovery</string>
    <string name="patch_set_superkey">Ustaw SuperKey</string>
    <string name="reboot_bootloader">Restartuj do Bootloadera</string>
    <string name="home_apatch_version">Wersja APatch</string>
    <string name="home_auth_key_title">Wpisz SuperKey</string>
    <string name="apm_reboot_to_apply">Uruchom ponownie aby zadziałało</string>
    <string name="apm_empty">Brak zainstalowanych modułów</string>
    <string name="home_working">Włączony</string>
    <string name="home_fingerprint">Odcisk palca</string>
    <string name="home_selinux_status_unknown">Nieznany</string>
    <string name="su_pkg_excluded_setting_title">Wyklucz modyfikacje</string>
    <string name="kpm_kp_not_installed">KernelPatch jest niezainstalowany</string>
    <string name="clear_super_key">Wyczyść SuperKey</string>
    <string name="patch_warnning">Instalacja wiąże się z ryzykiem. Upewnij się, że utworzono kopię zapasową danych.</string>
    <string name="safe_mode">Tryb bezpieczny</string>
    <string name="home_install_unknown">Nie zainstalowano lub nie uwierzytelniono</string>
    <string name="home_ap_cando_update">Aktualizacja</string>
    <string name="kpm_unload_confirm">Rozpakować \"%s\" moduł?</string>
    <string name="kpm_desc">Opis</string>
    <string name="kpm_args">Argumenty</string>
    <string name="su_selinux_via_hook">ominięcie za pomocą haka</string>
    <string name="su_pkg_excluded_label">wyklucz</string>
    <string name="apm_desc">Opis</string>
    <string name="settings_app_language">Język</string>
    <string name="system_default">Domyślne ustawienie systemowe</string>
    <string name="apm_not_installed">AndroidPatch jest niezainstalowany</string>
    <string name="home_patch_set_key_desc">Tylko dane uwierzytelniające dla KernelPatch</string>
    <string name="home_auth_key_desc">Rozpocznij po uzyskaniu certyfikatu</string>
    <string name="kpm_unload">Rozpakuj</string>
    <string name="patch">Załataj</string>
    <string name="settings_global_namespace_mode">Tryb globalnej przestrzeni nazw</string>
    <string name="settings_global_namespace_mode_summary">Wszystkie sesje root korzystają z globalnej przestrzeni montowania nazw</string>
    <string name="settings_clear_super_key_dialog">Czy na pewno chcesz kontynuować?</string>
    <string name="kpatch_version_update">Wersja: %s -&gt; %s</string>
    <string name="home_kpatch_info_title">Informacje</string>
    <string name="patch_select_bootimg_btn">Wybierz rozruch</string>
    <string name="patch_embed_kpm_btn">Osadź KPM</string>
    <string name="patch_start_patch_btn">Start</string>
    <string name="patch_start_unpatch_btn">Odłatkowanie</string>
    <string name="patch_item_error">!!BŁĄD!!</string>
    <string name="patch_item_bootimg">obraz boot</string>
    <string name="patch_item_bootimg_slot">slot:</string>
    <string name="patch_item_bootimg_dev">urządzenie:</string>
    <string name="patch_item_kernel">Jądro</string>
    <string name="patch_item_kpimg">kpimg</string>
    <string name="patch_item_kpimg_version">Wersja:</string>
    <string name="patch_item_kpimg_comile_time">Czas:</string>
    <string name="patch_item_kpimg_config">Konfiguracja:</string>
    <string name="patch_item_new_extra_kpm">osadź nowy</string>
    <string name="patch_item_existed_extra_kpm">istniejący</string>
    <string name="patch_item_extra_name">Nazwa:</string>
    <string name="patch_item_extra_version">Wersja:</string>
    <string name="patch_item_extra_author">Autor:</string>
    <string name="patch_item_extra_kpm_license">Licencja:</string>
    <string name="patch_item_extra_kpm_desciption">Opis:</string>
    <string name="patch_item_extra_args">Argumenty:</string>
    <string name="patch_item_extra_event">Wydarzenie:</string>
    <string name="patch_item_skey">SuperKey</string>
    <string name="kpatch_shadow_path_title">kpatch</string>
    <string name="patch_item_set_skey_label">SuperKey powinien mieć od 8 do 63 znaków i zawierać zarówno cyfry, jak i litery, ale bez znaków specjalnych.</string>
    <string name="home_ap_cando_reboot">Uruchom ponownie</string>
    <string name="patch_config_title">Łatki</string>
    <string name="patch_mode_bootimg_patch">Tryb: Łatka</string>
    <string name="patch_mode_patch_and_install">Tryb: Aktualizacja</string>
    <string name="patch_mode_uninstall_patch">Tryb: Odłatkowanie</string>
    <string name="teal_theme">Morski</string>
    <string name="hide_apatch_dialog_new_manager_name">Nowa nazwa menedżera</string>
    <string name="hide_apatch_dialog_summary">Będzie używana jako nowa nazwa aplikacji pokazana w Launcherze</string>
    <string name="setting_reset_su_path">Zresetuj ścieżkę su</string>
    <string name="enable_web_debugging">Włącz debugowanie WebView</string>
    <string name="mode_select_page_select_file">Wybierz obraz boot do załatania</string>
    <string name="mode_select_page_patch_and_install">Załataj i zainstaluj</string>
    <string name="settings_check_update">Sprawdź dostępność aktualizacji</string>
    <string name="settings_check_update_summary">Automatycznie sprawdzaj dostępność aktualizacji podczas otwierania aplikacji</string>
    <string name="home_install_unknown_summary">Kliknij, aby zainstalować</string>
    <string name="patch_mode_install_to_next_slot">Tryb: Instaluj do nieaktywnego slotu (po aktualizacji OTA)</string>
    <string name="kpm_add_kpm">Dodaj KPM</string>
    <string name="kpm_embed">Osadź</string>
    <string name="kpm_control">Kontrola</string>
    <string name="hide_apatch_manager">Ukryj menedżer APatch</string>
    <string name="hide_apatch_manager_summary">Zainstaluj aplikację proxy z losowym ID pakietu i niestandardową etykietą</string>
    <string name="mode_select_page_install_inactive_slot_warning">Twoje urządzenie będzie **ZMUSZONE** do uruchomienia się w bieżącym nieaktywnym slocie po ponownym uruchomieniu!\nUżywaj tej opcji tylko po zakończeniu aktualizacji OTA.\nKontynuować?</string>
    <string name="about_telegram_channel">Kanał na Telegramie</string>
    <string name="about_app_version">Wersja %1$s</string>
    <string name="about_telegram_group">Grupa na Telegramie</string>
    <string name="about_powered_by">Zasilany przez %1$s</string>
    <string name="red_theme">Czerwony</string>
    <string name="setting_reset_su_new_path">Nowa pełna ścieżka</string>
    <string name="home_more_menu_about">O aplikacji</string>
    <string name="apm_webui_open">Otwórz</string>
    <string name="green_theme">Zielony</string>
    <string name="home_dialog_uninstall_ap_only">Usuń AndroidPatch</string>
    <string name="home_dialog_uninstall_all">Odinstaluj wszystko</string>
    <string name="kpm_control_dialog_title">Kontroluj KPModule</string>
    <string name="kpm_control_dialog_content">Wpisz parametry kontrolne:</string>
    <string name="kpm_control_paramters">Parametry</string>
    <string name="kpm_control_outMsg">Wiadomość zwrotna</string>
    <string name="kpm_control_ok">Operacja przebiegła pomyślnie!</string>
    <string name="kpm_control_failed">Operacja nie powiodła się!</string>
    <string name="settings_night_mode_follow_sys">Podążaj za ciemnym motywem systemu</string>
    <string name="settings_night_mode_follow_sys_summary">Automatyczne przełącz na ciemny motyw na podstawie ustawień systemowych</string>
    <string name="settings_night_theme_enabled">Włącz ciemny motyw</string>
    <string name="settings_use_system_color_theme">Motyw kolorów systemu</string>
    <string name="settings_use_system_color_theme_summary">Używaj motywu kolorystycznego wygenerowanego przez system na podstawie bieżącej tapety</string>
    <string name="settings_custom_color_theme">Motyw kolorystyczny</string>
    <string name="blue_grey_theme">Niebiesko-szary</string>
    <string name="blue_theme">Niebieski</string>
    <string name="brown_theme">Brązowy</string>
    <string name="cyan_theme">Cyjanowy</string>
    <string name="deep_orange_theme">Ciemny pomarańczowy</string>
    <string name="deep_purple_theme">Ciemny fiolet</string>
    <string name="indigo_theme">Indygo</string>
    <string name="light_blue_theme">Jasnoniebieski</string>
    <string name="light_green_theme">Jasnozielony</string>
    <string name="pink_theme">Różowy</string>
    <string name="purple_theme">Fioletowy</string>
    <string name="yellow_theme">Żółty</string>
    <string name="amber_theme">Bursztynowy</string>
    <string name="lime_theme">Limonkowy</string>
    <string name="orange_theme">Pomarańczowy</string>
    <string name="hide_apatch_manager_failure">Nie udało się ukryć. Zgłoś błąd!</string>
    <string name="enable_web_debugging_summary">Może być używane do debugowania WebUI. Włącz tylko w razie potrzeby.</string>
    <string name="settings_donot_store_superkey">Nie przechowuj SuperKey lokalnie</string>
    <string name="settings_donot_store_superkey_summary">Uwierzytelniaj SuperKey przy każdym uruchomieniu menadżera</string>
    <string name="home_new_apatch_found">Dostępna jest nowa wersja %s. Kliknij, aby uaktualnić.</string>
    <string name="mode_select_page_title">Zainstaluj</string>
    <string name="mode_select_page_install_inactive_slot">Zainstaluj do nieaktywnego slotu (po aktualizacji OTA)</string>
    <string name="sakura_theme">Sakura</string>
    <string name="home_dialog_auth_fail_content">Nie można uwierzytelnić SuperKey, dlatego APatch nie może działać poprawnie.\nOto kilka możliwych przyczyn niepowodzenia uwierzytelniania:\n1. Nie zaktualizowano pliku boot.img za pomocą KernelPatch.\n2. Poprawiony plik boot.img nie został sflashowany.\n3. Klucz SuperKey jest nieprawidłowy lub zawiera znaki specjalne.\n4. Urządzenie nie obsługuje APatch i KernelPatch.\n\nSprawdź i spróbuj ponownie. Jeśli nadal występują problemy, zawsze możesz zadawać pytania na oficjalnej stronie repozytorium.</string>
    <string name="home_more_menu_feedback_or_suggestion">Opinie bądź sugestie</string>
    <string name="about_app_desc">Rozwiązanie rootowania jądra z jedynie okrojonym obrazem jądra, zapewniające możliwość montowania modułu systemowego i hakowania funkcji jądra.</string>
    <string name="success">Zakończono sukcesem</string>
    <string name="failure">Niepowodzenie</string>
    <string name="home_device_info">Urządzenie</string>
    <string name="home_system_version">Wersja systemu</string>
    <string name="kpm_install">Instaluj</string>
    <string name="home_dialog_auth_fail_title">Uwierzytelnianie nie powiodło się</string>
    <string name="home_dialog_uninstall_title">Odinstaluj</string>
    <string name="home_kpatch_version">Wersja KernelPatch</string>
    <string name="save_log">Zapisz logi</string>
    <string name="apm_action">Akcja</string>
    <string name="log_saved">Zapisano logi</string>
    <string name="crash_handle_title">Aplikacja się wysypała…</string>
    <string name="crash_handle_copy">Kopiuj logi awarii</string>
    <string name="crash_handle_copied">Logi awarii skopiowano do schowka.</string>
    <string name="settings_lite_mode">Włącz tryb uproszczony</string>
    <string name="settings_lite_mode_mode_summary">Funkcje modułu AP mogą ulec uszkodzeniu po włączeniu, ale czasami mogą skutecznie uniknąć wykrycia. Wymagany jest restart.</string>
    <string name="settings_force_overlayfs_mode">Wymuś użycie OverlayFS</string>
    <string name="settings_force_overlayfs_mode_summary">Użyj OverlayFS do montowania modułów AP zamiast Magic Mount, dopóki jądro to obsługuje. Wymagane jest ponowne uruchomienie.</string>
</resources>
