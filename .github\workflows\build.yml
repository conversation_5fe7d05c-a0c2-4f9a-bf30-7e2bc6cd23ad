name: Build Manager

on:
  push:
    tags: [ "*" ]
    branches: [ "main" ]
    paths:
      - '.github/workflows/build.yml'
      - 'app/**'
      - 'apd/**'
      - 'build.gradle.kts'
      - 'gradle/libs.versions.toml'
  pull_request:
    branches: [ "main" ]
    paths:
      - '.github/workflows/build.yml'
      - 'app/**'
      - 'apd/**'
      - 'build.gradle.kts'
      - 'gradle/libs.versions.toml'
  workflow_call:
  workflow_dispatch:

jobs:
  build-manager:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate version
        id: parse_version
        run: |
          COMMIT_NUM=$(git rev-list --count HEAD)
          VERSION=$(echo "$COMMIT_NUM + 200 + 10000" | bc)
          echo "Generated Version: $VERSION"
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Setup ninja
        uses: seanmiddleditch/gha-setup-ninja@v6
        with:
          version: 1.12.1

      - name: Setup Android SDK
        uses: android-actions/setup-android@v3
        with:
          packages: ''

      - name: Install toolchain
        run: |
          rustup default stable
          rustup update stable
          cargo install cargo-ndk
          rustup target install aarch64-linux-android

      - name: Cache Rust
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: apd
          cache-targets: false

      - name: Build with Gradle
        run: |
          echo 'org.gradle.parallel=true' >> gradle.properties
          echo 'org.gradle.vfs.watch=true' >> gradle.properties
          echo 'org.gradle.jvmargs=-Xmx2048m' >> gradle.properties
          echo 'android.native.buildOutput=verbose' >> gradle.properties
          sed -i 's/org.gradle.configuration-cache=true//g' gradle.properties
          ./gradlew clean assembleRelease
          echo "BUILD_TOOL_VERSION=$(ls /usr/local/lib/android/sdk/build-tools/ | tail -n 1)" >> $GITHUB_ENV

      - name: Sign Release
        env:
          SIGNING_KEY: ${{ secrets.SIGNING_KEY }}
        if: ${{ env.SIGNING_KEY != '' }}
        continue-on-error: true
        uses: noriban/sign-android-release@v5.1
        id: sign_app
        with:
          releaseDirectory: app/build/outputs/apk/release
          signingKeyBase64: ${{ secrets.SIGNING_KEY }}
          alias: ${{ secrets.ALIAS }}
          keyStorePassword: ${{ secrets.KEY_STORE_PASSWORD }}
          keyPassword: ${{ secrets.KEY_PASSWORD }}

      - name: Upload build artifact
        env:
          SIGNING_KEY: ${{ secrets.SIGNING_KEY }}
        if: ${{ env.SIGNING_KEY != '' }}
        uses: actions/upload-artifact@v4
        with:
          name: APatch
          path: ${{steps.sign_app.outputs.signedReleaseFile}}

      - name: Post to channel
        if: ${{github.event_name != 'pull_request' && github.ref == 'refs/heads/main' && github.ref_type != 'tag'}}
        env:
          BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
          COMMIT_MESSAGE: ${{ github.event.head_commit.message }}
          COMMIT_URL: ${{ github.event.head_commit.url }}
          COMMIT_ID: ${{ github.event.head_commit.id }}
        run: |
          if [ ! -z "${{ secrets.BOT_TOKEN }}" ]; then
            OUTPUT="app/build/outputs/apk/release"
            export Release=$(find $OUTPUT -name "*.apk")
            URL=$(python3 .github/scripts/telegram_url.py -1002058433411)
            curl -v "$URL" -F Release=@${{ steps.sign_app.outputs.signedReleaseFile }}
            URL=$(python3 .github/scripts/telegram_url.py -1001910818234)
            curl -v "$URL" -F Release=@${{ steps.sign_app.outputs.signedReleaseFile }}
          fi

      - name: Release apk
        env:
          SIGNING_KEY: ${{ secrets.SIGNING_KEY }}
        if: ${{ env.SIGNING_KEY != '' && github.ref_type == 'tag' }}
        continue-on-error: true
        uses: ncipollo/release-action@v1
        with:
          token: ${{ github.token }}
          tag: ${{ steps.parse_version.outputs.VERSION }}
          artifacts: ${{steps.sign_app.outputs.signedReleaseFile}}
          generateReleaseNotes: true
          makeLatest: true
          replacesArtifacts: true
