<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name" translatable="false">APatch</string>
    <string name="android_patch">Android 修補</string>
    <string name="patch_warnning">安裝過程存在風險。請確保你的資料已備份。</string>
    <string name="reboot_download">重新啟動至 Download</string>
    <string name="reboot_edl">重新啟動至 EDL</string>
    <string name="reboot_recovery">重新啟動至 Recovery</string>
    <string name="kernel_patch">核心修補</string>
    <string name="about">關於</string>
    <string name="reboot">重新啟動</string>
    <string name="home">首頁</string>
    <string name="settings">設定</string>
    <string name="reboot_bootloader">重新啟動至 Bootloader</string>
    <string name="apm_reboot_to_apply">重新啟動以應用變更</string>
    <string name="kpm_unload_confirm">確定要刪除「%s」模組嗎？</string>
    <string name="apm_uninstall_success">已解除安裝 %s</string>
    <string name="apm_install">安裝</string>
    <string name="kpatch_version">版本：%s</string>
    <string name="apm_remove">解除安裝</string>
    <string name="kpm_kp_not_installed">KernelPatch 尚未安裝</string>
    <string name="home_install_unknown">未安裝或未鑒權</string>
    <string name="apm_uninstall_confirm">確定要解除安裝「%s」模組？</string>
    <string name="su_pkg_excluded_setting_title">排除修改</string>
    <string name="home_click_to_learn_apatch">學習有關 APatch 的功能及使用方式</string>
    <string name="apm_version">版本</string>
    <string name="su_pkg_excluded_setting_summary">啟用此選項將允許 APatch 還原此應用程式被模組修改的任何檔案。</string>
    <string name="home_installing">安裝中</string>
    <string name="apm_start_downloading">開始下載 %s</string>
    <string name="su_pkg_excluded_label">忽略</string>
    <string name="su_title">超級使用者</string>
    <string name="kpm_desc">說明</string>
    <string name="su_selinux_via_hook">透過 Hook 忽略請求的方式</string>
    <string name="home_ap_cando_uninstall">解除安裝</string>
    <string name="kpm_args">引數</string>
    <string name="home_fingerprint">指紋</string>
    <string name="apatch_version">版本：%s</string>
    <string name="apm_overlay_fs_not_available">裝置核心停用了 OverlayFS，你無法使用模組功能!</string>
    <string name="kpm_version">版本</string>
    <string name="su_refresh">重新整理</string>
    <string name="su_show_system_apps">顯示系統應用程式</string>
    <string name="home_ap_cando_install">安裝</string>
    <string name="home_learn_apatch">深入瞭解 APatch</string>
    <string name="apm_failed_to_disable">無法停用「%s」模組</string>
    <string name="home_click_to_install">點擊以開始安裝</string>
    <string name="home_selinux_status_unknown">未知</string>
    <string name="home_selinux_status_permissive">允許執行</string>
    <string name="home_need_update">已有新版本可供使用</string>
    <string name="home_selinux_status">SELinux 狀態</string>
    <string name="apm_changelog">《更新紀錄》</string>
    <string name="apm_uninstall_failed">無法解除安裝 %s</string>
    <string name="kpm_author">作者</string>
    <string name="apm_failed_to_enable">無法啟用「%s」模組</string>
    <string name="apm_downloading">正在下載「%s」模組……</string>
    <string name="su_hide_system_apps">隱藏系統應用程式</string>
    <string name="apm_magisk_conflict">與 Magisk 發生衝突，你無法使用模組功能!</string>
    <string name="apm_desc">說明</string>
    <string name="home_ap_cando_update">更新</string>
    <string name="kpm_load_toast_failed">匯入失敗</string>
    <string name="kpm_unload">刪除</string>
    <string name="home_selinux_status_disabled">關閉</string>
    <string name="apm_new_version_available">點擊以安裝新版本的 %s。</string>
    <string name="apatch_version_update">版本：%s → %s</string>
    <string name="home_working">運作中 😋</string>
    <string name="apm_empty">尚未安裝模組</string>
    <string name="kpm_load">匯入</string>
    <string name="apm_update">更新</string>
    <string name="home_kernel">核心</string>
    <string name="home_selinux_status_enforcing">強制執行</string>
    <string name="kpm_license">授權</string>
    <string name="kpm_apm_empty">尚無已匯入的模組</string>
    <string name="home_manager_version">管理器版本</string>
    <string name="kpm_load_toast_succ">匯入成功</string>
    <string name="apm_author">作者</string>
    <string name="patch_title">修補</string>
    <string name="send_log">發送日誌</string>
    <string name="home_auth_key_desc">認證通過後開始執行</string>
    <string name="home_patch_set_key_desc">執行 KernelPatch 時所需的唯一憑證</string>
    <string name="apm">AP模組</string>
    <string name="safe_mode">安全模式</string>
    <string name="super_key">超級密鑰</string>
    <string name="home_apatch_version">APatch 版本</string>
    <string name="patch_set_superkey">設定超級密鑰</string>
    <string name="home_su_path">可執行 SU 路徑</string>
    <string name="home_patch_next_step">下一步</string>
    <string name="home_auth_key_title">輸入超級密鑰</string>
    <string name="home_not_installed">尚未安裝</string>
    <string name="kpm">KP模組</string>
    <string name="about_source_code"><![CDATA[<p>在%1$s<p/> 查看原始碼加入我們的 %2$s 頻道<p/>加入我們的 %3$s 群組]]></string>
    <string name="clear_super_key">清除超級密鑰</string>
    <string name="apm_not_installed">AndroidPatch 尚未安裝</string>
    <string name="system_default">系統預設</string>
    <string name="settings_app_language">語言</string>
    <string name="patch">修補</string>
    <string name="settings_global_namespace_mode_summary">所有 Root 會話將會全域掛載命名空間</string>
    <string name="settings_global_namespace_mode">全域命名空間模式</string>
    <string name="settings_clear_super_key_dialog">你確定要繼續嗎？</string>
    <string name="kpatch_version_update">版本：%s -&gt; %s</string>
    <string name="kpatch_shadow_path_title">kpatch</string>
    <string name="home_kpatch_info_title">資訊</string>
    <string name="home_ap_cando_reboot">重新啟動</string>
    <string name="patch_config_title">補丁</string>
    <string name="patch_mode_patch_and_install">模式：更新</string>
    <string name="patch_select_bootimg_btn">選擇 Boot 镜像</string>
    <string name="patch_embed_kpm_btn">嵌入核心模組</string>
    <string name="patch_start_patch_btn">開始</string>
    <string name="patch_item_error">！！錯誤！！</string>
    <string name="patch_item_bootimg">Boot 鏡像</string>
    <string name="patch_item_bootimg_slot">插槽：</string>
    <string name="patch_item_bootimg_dev">裝置:</string>
    <string name="patch_item_kernel">核心</string>
    <string name="patch_item_kpimg">kpimg</string>
    <string name="patch_item_new_extra_kpm">嵌入新的</string>
    <string name="patch_item_existed_extra_kpm">已嵌入</string>
    <string name="patch_item_extra_name">名稱：</string>
    <string name="patch_item_extra_version">版本：</string>
    <string name="patch_item_extra_author">作者：</string>
    <string name="patch_item_extra_kpm_license">授權：</string>
    <string name="patch_item_extra_kpm_desciption">描述：</string>
    <string name="patch_item_extra_event">事件：</string>
    <string name="patch_item_skey">超級密鑰</string>
    <string name="patch_item_set_skey_label">超級密鑰至少需要８個字符，並且同時包含數字和字母。</string>
    <string name="patch_item_extra_args">參數：</string>
    <string name="patch_mode_uninstall_patch">模式：撤銷補丁</string>
    <string name="patch_mode_bootimg_patch">模式：修補</string>
    <string name="patch_start_unpatch_btn">取消修補</string>
    <string name="patch_item_kpimg_version">版本：</string>
    <string name="patch_item_kpimg_comile_time">時間：</string>
    <string name="patch_item_kpimg_config">設定：</string>
    <string name="hide_apatch_manager">隱藏 APatch 管理器</string>
    <string name="hide_apatch_dialog_new_manager_name">新管理器名稱</string>
    <string name="hide_apatch_dialog_summary">它將用作啟動器中顯示的新應用程式標籤</string>
    <string name="hide_apatch_manager_summary">使用隨機套件 ID 和自訂應用程式標籤安裝新的管理器應用程式</string>
    <string name="hide_apatch_manager_failure">隱藏失敗，請回報bug！</string>
    <string name="kpm_embed">嵌入</string>
    <string name="kpm_add_kpm">新增 KPM</string>
    <string name="kpm_install">安裝</string>
    <string name="setting_reset_su_path">重置 su 路徑</string>
    <string name="setting_reset_su_new_path">新的完整路徑</string>
    <string name="success">成功</string>
    <string name="failure">失敗</string>
    <string name="settings_check_update">檢查更新</string>
    <string name="mode_select_page_patch_and_install">修補並安裝</string>
    <string name="mode_select_page_install_inactive_slot">安裝到非活動插槽（OTA 之後）</string>
    <string name="mode_select_page_select_file">選擇要修補的「boot image」</string>
    <string name="home_dialog_auth_fail_title">鑒權失敗</string>
    <string name="enable_web_debugging">啟用 WebView 偵錯</string>
    <string name="home_more_menu_feedback_or_suggestion">回饋或建議</string>
    <string name="home_more_menu_about">關於</string>
    <string name="mode_select_page_install_inactive_slot_warning">重新啟動後，您的裝置將**強制**啟動到目前非活動插槽！
\n僅在 OTA 完成之後使用此選項。
\n繼續？</string>
    <string name="settings_check_update_summary">開啟應用程式時自動檢查更新</string>
    <string name="apm_webui_open">打開網頁介面</string>
    <string name="enable_web_debugging_summary">可用於偵錯WebUI，請僅在需要時啟用。</string>
    <string name="settings_donot_store_superkey">停用本機儲存密鑰</string>
    <string name="home_dialog_auth_fail_content">無法驗證超級密鑰，因此 APatch 無法正常運作。 \n以下是驗證失敗的一些可能原因： \n1. 您尚未使用 KernelPatch 程式修補啟動映像檔。 \n2. 修補後的啟動映像檔未刷入裝置。 \n3. 超級密鑰錯誤。 \n4. 您的裝置不支援 APatch 和 KernelPatch。 \n \n請檢查並再次嘗試。如果仍存在問題，您隨時可以在官方存放庫的議題頁面上提問。</string>
    <string name="home_install_unknown_summary">點擊以安裝</string>
    <string name="home_device_info">裝置</string>
    <string name="home_new_apatch_found">新版本可用：%s，點選升級。</string>
    <string name="mode_select_page_title">安裝</string>
    <string name="home_dialog_uninstall_title">解除安裝</string>
    <string name="home_dialog_uninstall_ap_only">僅刪除 AndroidPatch</string>
    <string name="home_dialog_uninstall_all">完全卸載</string>
    <string name="home_system_version">系統版本</string>
    <string name="home_kpatch_version">KernelPatch 版本</string>
    <string name="settings_donot_store_superkey_summary">每次管理器啟動時驗證超級密鑰</string>
    <string name="patch_mode_install_to_next_slot">模式：安裝到另一個插槽（OTA之後）</string>
    <string name="kpm_control">控制</string>
    <string name="about_telegram_group">Telegram 群組</string>
    <string name="about_powered_by">由 %1$s 提供支援</string>
    <string name="kpm_control_dialog_content">輸入控制參數：</string>
    <string name="settings_night_theme_enabled">啟用深色主題</string>
    <string name="settings_use_system_color_theme_summary">使用由系統根據月前桌布生成的顏色主題</string>
    <string name="deep_purple_theme">深紫色主題</string>
    <string name="light_blue_theme">淺藍色主題</string>
    <string name="indigo_theme">靛藍色主題</string>
    <string name="about_app_desc">核心級 ROOT 解決方案，無需重新編譯核心映像，同時提供掛載系統模組和掛鉤核心功能的能力。</string>
    <string name="kpm_control_failed">操作失敗！</string>
    <string name="cyan_theme">青色主題</string>
    <string name="kpm_control_paramters">參數</string>
    <string name="kpm_control_outMsg">訊息</string>
    <string name="kpm_control_ok">操作成功！</string>
    <string name="settings_night_mode_follow_sys">遵循系統的深色主題</string>
    <string name="settings_night_mode_follow_sys_summary">根據系統設定自動切換為深色主題</string>
    <string name="settings_use_system_color_theme">系統顏色主題</string>
    <string name="settings_custom_color_theme">顏色主題</string>
    <string name="amber_theme">琥珀色主題</string>
    <string name="blue_grey_theme">藍灰色主題</string>
    <string name="brown_theme">棕色主題</string>
    <string name="deep_orange_theme">深橙色主題</string>
    <string name="green_theme">綠色主題</string>
    <string name="light_green_theme">淺綠色主題</string>
    <string name="lime_theme">檸檬色主題</string>
    <string name="orange_theme">橙色主題</string>
    <string name="pink_theme">粉紅色主題</string>
    <string name="purple_theme">紫色主題</string>
    <string name="red_theme">紅色主題</string>
    <string name="sakura_theme">櫻花色主題</string>
    <string name="teal_theme">青綠色主題</string>
    <string name="about_app_version">版本 %1$s</string>
    <string name="about_telegram_channel">Telegram 頻道</string>
    <string name="blue_theme">藍色主題</string>
    <string name="yellow_theme">黃色主題</string>
    <string name="kpm_control_dialog_title">控制 KPModule</string>
    <string name="save_log">儲存日誌</string>
    <string name="log_saved">日誌已儲存</string>
    <string name="crash_handle_title">應用程式崩潰..</string>
    <string name="crash_handle_copy">複製崩潰日誌</string>
    <string name="apm_action">執行</string>
    <string name="crash_handle_copied">已將崩潰日誌複製到剪貼簿。</string>
</resources>
