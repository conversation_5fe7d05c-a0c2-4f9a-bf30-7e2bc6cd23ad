# Поширені запитання


## Що таке APatch?
APatch - це root рішення, схоже на Magisk або KernelSU, яке поєднує найкраще з обох.
Воно поєднує зручний і простий метод установки Magisk через boot.img з потужними можливостями виправлення ядра KernelSU.

## У чому різниця між APatch та Magisk?
- Magisk змінює систему ініціалізації за допомогою патча на RAM диску вашого завантажувального образу, в той час як APatch вносить зміни безпосередньо в ядро.


## APatch проти KernelSU
- Для KernelSU потрібен вихідний код ядра вашого пристрою, який не завжди надається OEM-виробником. APatch працює безпосередньо з вашим вихідним `boot.img`.


## APatch проти Magisk, KernelSU
- APatch дозволяє вам за бажання не змінювати SELinux. Це означає, що потік програми може бути рутований, в libsu і IPC немає необхідності.
- **Kernel Patch Module** надається.


## Що таке Kernel Patch Module?
Деякий код виконується у просторі ядра, аналогічно модулям ядра (Loadable Kernel Modules, LKM).

Крім того, KPM надає можливість виконувати inline-hook, syscall-table-hook у просторі ядра.

Для отримання додаткової інформації дивіться [Як написати KPM](https://github.com/bmax121/KernelPatch/blob/main/doc/module.md)


## Зв'язок між APatch та KernelPatch

APatch заснований на KernelPatch, успадкував усі його можливості та був розширений.

Ви можете встановити тільки KernelPatch, але це не дозволить вам використовувати модулі Magisk, а щоб використовувати керування суперкористувачем, вам необхідно встановити AndroidPatch, а потім видалити його.

[Дізнатись більше про KernelPatch](https://github.com/bmax121/KernelPatch)


## Що таке SuperKey?
KernelPatch додає новий системний дзвінок (syscall) для надання всіх можливостей додаткам і програмам в просторі користувача. Цей системний виклик називається **SuperCall**.
Коли програма намагається викликати **SuperCall**, їй необхідно надати облікові дані для доступу, які називають **SuperKey**.
**SuperCall** може бути успішно викликаний тільки в тому випадку, якщо **SuperKey** правильний, а в іншому випадку об'єкт, що викликає, не змінюється.


## Як щодо SELinux?
- KernelPatch не змінює контекст SELinux та обходить SELinux за допомогою перехоплення.
  Це дозволяє вам рутувати потік Android у контексті програми без необхідності використовувати libsu для запуску нового процесу та подальшого виконання IPC.
  Це дуже зручно.
- Крім того, APatch безпосередньо використовує magiskpolicy для забезпечення додаткової підтримки SELinux.
