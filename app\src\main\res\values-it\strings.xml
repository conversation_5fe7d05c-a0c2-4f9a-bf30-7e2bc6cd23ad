<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="apm_reboot_to_apply">Riavvia per rendere effettive le modifiche</string>
    <string name="android_patch">Patch di Android</string>
    <string name="kpm_unload_confirm">Rimuovi modulo \"%s\"?</string>
    <string name="apm_uninstall_success">%s disinstallato</string>
    <string name="send_log">Invia registro</string>
    <string name="apm_install">Installa</string>
    <string name="home_auth_key_desc">Avvia dopo la certificazione</string>
    <string name="kpatch_version">Versione: %s</string>
    <string name="apm_remove">Rimuovi</string>
    <string name="patch_warnning">L\'installazione comporta dei rischi. Assicurati che i tuoi dati siano stati salvati.</string>
    <string name="kpm_kp_not_installed">KernelPatch non installato</string>
    <string name="home_patch_set_key_desc">Credenziale solo per KernelPatch</string>
    <string name="home_install_unknown">Non installato o verificato</string>
    <string name="apm_uninstall_confirm">Disinstallare il modulo \"%s\"?</string>
    <string name="su_pkg_excluded_setting_title">Escludi modifiche</string>
    <string name="reboot_download">Riavvia in modalità Download</string>
    <string name="home_click_to_learn_apatch">Scopri le caratteristiche di APatch e come utilizzarlo</string>
    <string name="apm_version">Versione</string>
    <string name="su_pkg_excluded_setting_summary">Abilita questa opzione per permettere ad APatch di ripristinare qualsiasi file modificato dai moduli per questa applicazione.</string>
    <string name="reboot_edl">Riavvia in modalità EDL</string>
    <string name="home_installing">Installazione in corso</string>
    <string name="apm_start_downloading">Download di %s avviato</string>
    <string name="su_selinux_via_hook">Aggira tramite hook</string>
    <string name="home_ap_cando_uninstall">Disinstalla</string>
    <string name="apatch_version">Versione: %s</string>
    <string name="apm_overlay_fs_not_available">I moduli non sono disponibili poiché OverlayFS è disabilitato dal kernel</string>
    <string name="kpm_version">Versione</string>
    <string name="su_refresh">Ricarica</string>
    <string name="safe_mode">Modalità provvisoria</string>
    <string name="su_show_system_apps">Mostra app di sistema</string>
    <string name="home_ap_cando_install">Installa</string>
    <string name="reboot_recovery">Riavvia in Recovery</string>
    <string name="home_learn_apatch">Scopri APatch</string>
    <string name="apm_failed_to_disable">Impossibile disabilitare il modulo \"%s\"</string>
    <string name="kernel_patch">Patch di Kernel</string>
    <string name="home_click_to_install">Clicca per installare</string>
    <string name="home_selinux_status_unknown">Sconosciuto</string>
    <string name="about">Informazioni</string>
    <string name="reboot">Riavvio completo</string>
    <string name="home_need_update">Nuova versione disponibile</string>
    <string name="home_selinux_status">Stato di SELinux</string>
    <string name="apm_uninstall_failed">Impossibile disinstallare %s</string>
    <string name="kpm_author">Autore</string>
    <string name="apm_failed_to_enable">Impossibile abilitare il modulo \"%s\"</string>
    <string name="home">Home</string>
    <string name="patch_set_superkey">Imposta Superchiave</string>
    <string name="apm_downloading">Download del modulo \"%s\" in corso…</string>
    <string name="su_hide_system_apps">Nascondi app di sistema</string>
    <string name="apm_magisk_conflict">I moduli non sono disponibili a causa di un conflitto con Magisk!</string>
    <string name="home_ap_cando_update">Aggiorna</string>
    <string name="kpm_load_toast_failed">Caricamento fallito</string>
    <string name="home_selinux_status_disabled">Spento</string>
    <string name="settings">Impostazioni</string>
    <string name="apm_new_version_available">Clicca per installare la nuova versione %s.</string>
    <string name="apatch_version_update">Versione: %s -&gt; %s</string>
    <string name="home_working">In esecuzione 😋</string>
    <string name="apm_empty">Nessun modulo installato</string>
    <string name="kpm_load">Carica</string>
    <string name="home_patch_next_step">Avanti</string>
    <string name="home_auth_key_title">Inserisci Superchiave</string>
    <string name="apm_update">Aggiorna</string>
    <string name="home_not_installed">Non installato</string>
    <string name="kpm_license">Licenza</string>
    <string name="reboot_bootloader">Riavvia in modalità Bootloader</string>
    <string name="kpm_apm_empty">Nessun modulo caricato</string>
    <string name="about_source_code">&lt;![CDATA[&lt;p&gt;Visualizza codice sorgente %1$s&lt;p/&gt; Unisci il nostro %2$s canale &lt;p/&gt; Unisci il nostro %3$s gruppo]]] &gt;</string>
    <string name="home_manager_version">Versione del gestore</string>
    <string name="kpm_load_toast_succ">Caricamento riuscito</string>
    <string name="apm_author">Autore</string>
    <string name="su_pkg_excluded_label">escludi</string>
    <string name="su_title">Superutente</string>
    <string name="kpm_desc">Descrizione</string>
    <string name="kpm_args">Argomenti</string>
    <string name="home_fingerprint">Impronta digitale</string>
    <string name="apm_desc">Descrizione</string>
    <string name="kpm_unload">Rimuovi</string>
    <string name="home_kernel">Kernel</string>
    <string name="home_selinux_status_enforcing">Rinforzo</string>
    <string name="patch_title">Applica una patch</string>
    <string name="home_apatch_version">Versione di APatch</string>
    <string name="home_selinux_status_permissive">Permissivo</string>
    <string name="apm_changelog">Registro delle modifiche</string>
    <string name="home_su_path">Eseguibile su</string>
    <string name="apm">APModule</string>
    <string name="super_key">Superchiave</string>
    <string name="app_name" translatable="false">APatch</string>
    <string name="kpm">KPModule</string>
    <string name="clear_super_key">Elimina Superchiave</string>
    <string name="apm_not_installed">AndroidPatch non installato</string>
    <string name="system_default">Predefinito di sistema</string>
    <string name="settings_app_language">Lingua</string>
    <string name="patch">Applica una patch</string>
    <string name="settings_global_namespace_mode">Modalità namespace globale</string>
    <string name="settings_global_namespace_mode_summary">Tutte le sessioni di root erediteranno il namespace globale</string>
    <string name="settings_check_update">Controlla aggiornamenti</string>
    <string name="home_ap_cando_reboot">Riavvia</string>
    <string name="patch_config_title">Patch</string>
    <string name="patch_mode_uninstall_patch">Modalità: Disinstalla KPatch</string>
    <string name="patch_item_bootimg_slot">Slot:</string>
    <string name="patch_embed_kpm_btn">Incorpora KPM</string>
    <string name="patch_mode_patch_and_install">Modalità: Applica patch e installa</string>
    <string name="mode_select_page_select_file">Seleziona un’immagine di avvio a cui applicare la patch</string>
    <string name="patch_item_kpimg_comile_time">Tempo:</string>
    <string name="patch_item_extra_kpm_desciption">Descrizione:</string>
    <string name="patch_item_kernel">Kernel</string>
    <string name="patch_item_kpimg">Kpimg</string>
    <string name="patch_item_kpimg_version">Versione:</string>
    <string name="patch_item_new_extra_kpm">Incorpora nuovo</string>
    <string name="patch_item_existed_extra_kpm">Esistito</string>
    <string name="mode_select_page_install_inactive_slot_warning">Il dispositivo sarà **FORZATO** ad avviarsi nello slot inattivo dopo il riavvio!
\nUsa questa opzione solo dopo l\'OTA.
\nContinuare?</string>
    <string name="home_dialog_auth_fail_title">Autenticazione fallita</string>
    <string name="home_system_version">Versione di sistema</string>
    <string name="home_install_unknown_summary">Clicca per installare</string>
    <string name="home_device_info">Dispositivo</string>
    <string name="home_kpatch_version">Versione di KernelPatch</string>
    <string name="kpm_add_kpm">Aggiungi KPM</string>
    <string name="hide_apatch_manager_summary">Installa una nuova app di gestione con un ID pacchetto casuale e nome applicazione personalizzato</string>
    <string name="setting_reset_su_new_path">Nuovo percorso</string>
    <string name="home_new_apatch_found">Clicca per aggiornare alla nuova versione disponibile %s</string>
    <string name="mode_select_page_title">Installa</string>
    <string name="mode_select_page_patch_and_install">Applica patch e installa</string>
    <string name="kpm_embed">Incorpora</string>
    <string name="mode_select_page_install_inactive_slot">Installa nello slot inattivo (dopo OTA)</string>
    <string name="settings_donot_store_superkey">Non memorizzare la SuperKey in locale</string>
    <string name="patch_start_patch_btn">Inizia</string>
    <string name="kpatch_shadow_path_title">Kpatch</string>
    <string name="patch_item_kpimg_config">Configurazione:</string>
    <string name="hide_apatch_dialog_new_manager_name">Nuovo nome del gestore</string>
    <string name="settings_check_update_summary">Cerca aggiornamenti automaticamente all\'avvio dell\'app</string>
    <string name="patch_mode_install_to_next_slot">Modalità: Installa nello slot successivo (Dopo OTA)</string>
    <string name="enable_web_debugging">Attiva debug di WebView</string>
    <string name="settings_donot_store_superkey_summary">Richiedi la SuperKey ogni volta che il gestore viene avviato</string>
    <string name="patch_item_extra_version">Versione:</string>
    <string name="patch_item_extra_author">Autore:</string>
    <string name="patch_item_extra_event">Evento:</string>
    <string name="patch_item_extra_kpm_license">Licenza:</string>
    <string name="patch_item_bootimg_dev">Dispositivo:</string>
    <string name="patch_select_bootimg_btn">Seleziona avvio</string>
    <string name="hide_apatch_manager_failure">Errore nel nascondere l\'app, per favore segnala il bug!</string>
    <string name="kpm_install">Installa</string>
    <string name="patch_item_extra_args">Argomenti:</string>
    <string name="patch_item_skey">Super chiave</string>
    <string name="hide_apatch_dialog_summary">Sarà usato come nome dell\'applicazione mostrato nel launcher</string>
    <string name="failure">Fallimento</string>
    <string name="patch_item_bootimg">Bootimg</string>
    <string name="patch_start_unpatch_btn">Rimuovi patch</string>
    <string name="patch_item_set_skey_label">La super chiave dovrebbe essere di almeno 8 caratteri e includere sia numeri sia lettere, ma non caratteri speciali</string>
    <string name="setting_reset_su_path">Reimposta il percorso su</string>
    <string name="home_kpatch_info_title">Informazioni</string>
    <string name="settings_clear_super_key_dialog">Sei sicuro di voler procedere?</string>
    <string name="kpatch_version_update">Versione: %s -&gt; %s</string>
    <string name="apm_webui_open">Apri</string>
    <string name="enable_web_debugging_summary">Può essere usato per il Debug di WebUI, attiva solo se necessario.</string>
    <string name="patch_mode_bootimg_patch">Modalità: Applica patch</string>
    <string name="patch_item_error">!!ERRORE!!</string>
    <string name="patch_item_extra_name">Nome:</string>
    <string name="hide_apatch_manager">Nascondi il gestore di APatch</string>
    <string name="success">Successo</string>
    <string name="kpm_control_dialog_content">Parametri di controllo dell\'ingresso:</string>
    <string name="kpm_control_ok">Operazione riuscita!</string>
    <string name="settings_night_mode_follow_sys">Segui il Tema scuro di sistema</string>
    <string name="home_dialog_auth_fail_content">Impossibile autenticare la Super chiave, APatch non può funzionare correttamente.\nEcco alcuni possibili motivi per il fallimento dell\'autenticazione:\n1. Non hai applicato la patch all\'immagine di avvio con KernelPatch.\n2. L\'immagine di avvio con patch non è stata installata.\n3. Errata Super chiave.\n4. Il dispositivo non supporta APatch e KernelPatch.\nControlla e riprova. Se ci sono ancora problemi, puoi sempre fare domande sulla pagina dei problemi del repository ufficiale.</string>
    <string name="kpm_control_paramters">Parametri</string>
    <string name="settings_night_mode_follow_sys_summary">Passa automaticamente al Tema scuro usando le impostazioni di sistema</string>
    <string name="home_more_menu_about">Chi siamo</string>
    <string name="home_dialog_uninstall_title">Disinstalla</string>
    <string name="kpm_control">Controllo</string>
    <string name="home_more_menu_feedback_or_suggestion">Riscontro o suggerimento</string>
    <string name="home_dialog_uninstall_ap_only">Rimuovi AndroidPatch</string>
    <string name="home_dialog_uninstall_all">Disinstallare tutti</string>
    <string name="kpm_control_dialog_title">Controlla KPModule</string>
    <string name="kpm_control_outMsg">Messaggio</string>
    <string name="kpm_control_failed">Operazione fallita!</string>
    <string name="settings_night_theme_enabled">Abilita il Tema scuro</string>
    <string name="settings_use_system_color_theme">Colore del Tema di sistema</string>
    <string name="settings_use_system_color_theme_summary">Usa un colore generato dallo sfondo di sistema attuale</string>
    <string name="settings_custom_color_theme">Colore</string>
    <string name="amber_theme">Tema ambra</string>
    <string name="blue_grey_theme">Tema grigio blu</string>
    <string name="blue_theme">Tema blu</string>
    <string name="brown_theme">Tema marrone</string>
    <string name="cyan_theme">Tema ciano</string>
    <string name="deep_orange_theme">Tema arancione scuro</string>
    <string name="deep_purple_theme">Tema viola scuro</string>
    <string name="green_theme">Tema verde</string>
    <string name="indigo_theme">Tema indaco</string>
    <string name="light_blue_theme">Tema azzurro</string>
    <string name="light_green_theme">Tema verde chiaro</string>
    <string name="lime_theme">Tema lime</string>
    <string name="orange_theme">Tema arancione</string>
    <string name="pink_theme">Tema rosa</string>
    <string name="purple_theme">Tema viola</string>
    <string name="red_theme">Tema rosso</string>
    <string name="sakura_theme">Sakura</string>
    <string name="teal_theme">Tema verde acqua</string>
    <string name="yellow_theme">Tema giallo</string>
    <string name="about_app_version">Versione %1$s</string>
    <string name="about_app_desc">Soluzione Kernel Root con solo un\'immagine del kernel ridotta, che offre la possibilità di montare il modulo di sistema e agganciare la funzione kernel.</string>
    <string name="about_telegram_channel">Canale Telegram</string>
    <string name="about_telegram_group">Gruppo Telegram</string>
    <string name="save_log">Salva registro</string>
    <string name="about_powered_by">Gestito da %1$s</string>
    <string name="crash_handle_copied">Copia contenuto del registro di arresto anomalo negli appunti</string>
    <string name="log_saved">Registri salvati</string>
    <string name="crash_handle_title">App chiusa in modo anomalo..</string>
    <string name="crash_handle_copy">Copia registro di arresto anomalo</string>
    <string name="apm_action">Azione</string>
</resources>
