# الأسئلة الشائعة (FAQ)

## ما هو أباتش؟
أباتش هو حل روت شبيه بـ ماجيسك أو كيرنل إس يو، يجمع بين أفضل ما في كليهما.  
يستخدم طريقة التثبيت السهلة الخاصة بـ ماجيسك من خلال `boot.img`، مع قدرات تعديل النواة القوية التي يوفرها كيرنل إس يو.

## ما الفرق بين أباتش وماجيسك؟
- ماجيسك يقوم بتعديل نظام init عبر تعديل في ramdisk الخاص بملف boot image، بينما أباتش يقوم بتعديل النواة مباشرة.

## أباتش مقابل كيرنل إس يو
- كيرنل إس يو يحتاج إلى الكود المصدري للنواة الخاصة بجهازك، والذي لا توفره الشركات دائمًا.  
  أما أباتش فيعمل فقط باستخدام ملف `boot.img` الأصلي.

## أباتش مقابل ماجيسك وكيرنل إس يو
- يسمح لك أباتش بعدم تعديل SELinux اختياريًا، مما يعني أن خيوط التطبيق (APP thread) يمكن أن تحصل على صلاحيات الروت، دون الحاجة إلى libsu أو IPC.
- **وحدة تعديل النواة (Kernel Patch Module)** متوفرة.

## ما هي وحدة تعديل النواة (Kernel Patch Module)؟
بعض الشيفرات تعمل ضمن مساحة النواة، مشابهة لـ LKM (وحدات نواة قابلة للتحميل).  
بالإضافة إلى ذلك، توفر KPM القدرة على تنفيذ inline-hook و syscall-table-hook ضمن مساحة النواة.

لمزيد من المعلومات، راجع [كيفية كتابة KPM](https://github.com/bmax121/KernelPatch/blob/main/doc/module.md)

## العلاقة بين أباتش وKernelPatch
يعتمد أباتش على KernelPatch، ويرث جميع قدراته، وتم توسيعه ليشمل ميزات إضافية.  
يمكنك تثبيت KernelPatch فقط، لكنك لن تستطيع استخدام وحدات ماجيسك معه.

[تعرف أكثر على KernelPatch](https://github.com/bmax121/KernelPatch)

## ما هو SuperKey؟
يضيف KernelPatch نداء نظامي (syscall) جديد يمنح كافة الصلاحيات للتطبيقات والبرامج في مساحة المستخدم (userspace)، وهذا النداء يسمى **SuperCall**.  
عندما يحاول تطبيق أو برنامج استدعاء **SuperCall**، يجب أن يقدم بيانات اعتماد وصول تُعرف باسم **SuperKey**.  
يتم تنفيذ **SuperCall** بنجاح فقط إذا كانت **SuperKey** صحيحة، وإن لم تكن كذلك فلن يتأثر المستدعي.

## ماذا عن SELinux؟
- لا يقوم KernelPatch بتعديل سياق SELinux، بل يتجاوزه من خلال hook.  
  هذا يسمح بعمل روت لخيط (thread) داخل التطبيق دون الحاجة لاستخدام libsu لبدء عملية جديدة ثم إجراء IPC، مما يجعله مريحًا جدًا.
- بالإضافة إلى ذلك، يستخدم أباتش أداة magiskpolicy مباشرة لتوفير دعم إضافي لـ SELinux.
